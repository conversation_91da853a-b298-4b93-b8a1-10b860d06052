import { ethers, BigNumber, utils } from "ethers";
import { EventEmitter } from 'events';

import Client from "./lib/client";

import fs from "fs";
import { macro } from "./lib/macro";
import tools from "./lib/tools";
import { WhaleSwapData, WalletData, SwapResult } from "./lib/type/DataType";
import OracleV2 from "./lib/OracleV2";

import { ExContract, ExWallet } from "./lib/comp/EthersWrapper";
import Config from "./lib/config/Config";
import ConfigOne from "./lib/config/ConfigOne";
import ConfigOec from "./lib/config/ConfigOec";
import ConfigHeco from "./lib/config/ConfigHeco";
import ConfigPolygon from "./lib/config/ConfigPolygon";
import ConfigFtm from "./lib/config/ConfigFtm";
import ConfigCelo from "./lib/config/ConfigCelo";
import ConfigHoo from "./lib/config/ConfigHoo";
import ConfigXdai from "./lib/config/ConfigXdai";
import ConfigRose from "./lib/config/ConfigRose";
import ConfigAurora from "./lib/config/ConfigAurora";
import ConfigKcc from "./lib/config/ConfigKcc";
import ConfigVlx from "./lib/config/ConfigVlx";
import ConfigBch from "./lib/config/ConfigBch";
import DataHandle from "./lib/DataHandle";
import ConfigAstr from "./lib/config/ConfigAstr";
import ConfigWan from "./lib/config/ConfigWan";
import ConfigMilkada from "./lib/config/ConfigMilkada";
import ConfigSys from "./lib/config/ConfigSys";
import ConfigAvax from "./lib/config/ConfigAvax";
import ConfigKai from "./lib/config/ConfigKai";
import ConfigTomo from "./lib/config/ConfigTomo";
import ConfigMovr from "./lib/config/ConfigMovr";
import ConfigCube from "./lib/config/ConfigCube";
import PumpCube from "./lib/chain/PumpCube";
import ConfigDfk from "./lib/config/ConfigDfk";
import PumpDfk from "./lib/chain/PumpDfk";
import LocalLog from "./lib/comp/LocalLog";
import ConfigGlmr from "./lib/config/ConfigGlmr";
import ConfigDoge from "./lib/config/ConfigDoge";
import SignController from "./lib/prefabs/SignController";
import Lazy from "./lib/lazy/LazyController";
import ConfigEthw from "./lib/config/ConfigEthw";
import ConfigFitfi from "./lib/config/ConfigFitfi";
import ConfigGate from "./lib/config/ConfigGate";
import ConfigCro from "./lib/config/ConfigCro";
import ConfigCanto from "./lib/config/ConfigCanto";
import ConfigPom from "./lib/config/ConfigPom";
import ConfigKlay from "./lib/config/ConfigKlay";
import ConfigSama from "./lib/config/ConfigSama";
import ConfigRei from "./lib/config/ConfigRei";
import ConfigKub from "./lib/config/ConfigKub";
import ConfigBsc from "./lib/config/ConfigBsc";
import ConfigCore from "./lib/config/ConfigCore";
import ConfigLat from "./lib/config/ConfigLat";
import ConfigVs from "./lib/config/ConfigVs";
import ConfigArb from "./lib/config/ConfigArb";
import ConfigNova from "./lib/config/ConfigNova";
import ConfigZk from "./lib/config/ConfigZk";
import ConfigMetis from "./lib/config/ConfigMetis";
import RouterController from "./lib/router/RouterController";
import ConfigPls from "./lib/config/ConfigPls";
import ConfigKava from "./lib/config/ConfigKava";
import ConfigBase from "./lib/config/ConfigBase";
import ConfigOpbnb from "./lib/config/ConfigOpbnb";
import ConfigWemix from "./lib/config/ConfigWemix";
import ContractInstance from "./lib/contract/ContractInstance";
import ConfigMxc from "./lib/config/ConfigMxc";
import ConfigEla from "./lib/config/ConfigEla";
import ConfigZeta from "./lib/config/ConfigZeta";
import ConfigOp from "./lib/config/ConfigOp";
import ConfigDegen from "./lib/config/ConfigDegen";
import ConfigBevm from "./lib/config/ConfigBevm";
import ConfigX from "./lib/config/ConfigX";
import ConfigSei from "./lib/config/ConfigSei";
import BlackList from "./lib/comp/BlackList";
import ConfigS from "./lib/config/ConfigS";
import ConfigAbs from "./lib/config/ConfigAbs";

export default class bot {
    static mode = macro.BOT_MODE.DEFAULT;
    static chain = macro.CHAIN.OEC;
    
    static event = new EventEmitter();
    static localLog = new LocalLog();
    static config : Config;
    static client : Client;
    static oracleV2 : OracleV2;
    static handle : DataHandle;
    static contractIns = new ContractInstance();

    static routers : RouterController;

    static operators : ExWallet[] = [];
    static walletData : WalletData = new WalletData();
    static abiCoder : ethers.utils.AbiCoder;
    static signer = new SignController();

    static MAX_UPDATE_PAIR = 30000; //一次更新的pair数，避免爆内存

    static logLevel = 2; //0只显示重要log

    static cmd = "default"; //每台机器的标记

    static blackList = new BlackList();

    static async init(chain : macro.CHAIN){
        this.chain = chain;
        this.config = this.getConfig(chain);
        this.fixAddress();

        console.log(`# MODE : ${bot.mode}, #CMD : ${bot.cmd}`);

        switch(bot.mode){
            case macro.BOT_MODE.PUMP:
                bot.config.bot.active = false;
                bot.config.bot2.active = false;
                bot.config.trash.active = false;
                bot.config.pump.active = true;
                this.logLevel = 0;
                break;
            case macro.BOT_MODE.PUMP_WITH_DEBUG:
                bot.config.bot.active = false;
                bot.config.bot2.active = false;
                bot.config.trash.active = false;
                bot.config.pump.active = true;
                this.logLevel = 1;
                break;
            case macro.BOT_MODE.NO_PUMP:
                bot.config.trash.active = true;
                bot.config.pump.active = false;
                break;
            case macro.BOT_MODE.TRASH:
                console.log("###  TRASH MODE ###");
                bot.config.bot.active = false;
                bot.config.bot2.active = false;
                bot.config.trash.active = true;
                bot.config.pump.active = false;
                break;
            default: break;
        }

        if(bot.mode == macro.BOT_MODE.PAIR || bot.mode == macro.BOT_MODE.SKIM || bot.mode == macro.BOT_MODE.UPDATE || bot.mode == macro.BOT_MODE.MEV){
            bot.config.bot.active = false;
            bot.config.bot2.active = false;
            bot.config.trash.active = false;
            bot.config.pump.active = false;
            if(bot.mode == macro.BOT_MODE.UPDATE) console.log("MAX_UPDATE_PAIR: ", this.MAX_UPDATE_PAIR);
        }

        //初始化钱包
        let walletFile = `./src/wallets/${chain}.json`;
        if(!fs.existsSync(walletFile)) walletFile = `./src/wallets/oec.json`;
        this.walletData = JSON.parse(fs.readFileSync(walletFile, "utf-8").toLowerCase()) as WalletData;

        //bot.snipper = new Snipper();
        this.handle = new DataHandle();
        this.routers = new RouterController();

        //每隔2秒检查wss是否ready, ready了才继续后面的逻辑
        while (!this.handle.wss[0].isReady) {
            console.log("checking wss isReady.....");
            await new Promise(resolve => setTimeout(resolve, 2000));
        }

        for(let i = 0 ; i < bot.config.mainnet.wss.length; i++){
            this.operators[i] = this.newWallet(Object.values(this.walletData.oracle)[0], i);
        }

        this.abiCoder = new ethers.utils.AbiCoder();

        //初始化data文件夹
        fs.mkdirSync(`./src/data/${bot.chain}`, {recursive: true});

        //初始化oracle
        bot.oracleV2 = new OracleV2();
        await tools.delay(2000); //等待websocket连接完成

        if (bot.mode == macro.BOT_MODE.UPDATE || bot.mode == macro.BOT_MODE.MEV){
            await bot.oracleV2.init();
        } else {
            bot.oracleV2.init();
        }

        bot.client = new Client();
        //FilterOnBlock.init();
        if(bot.mode == macro.BOT_MODE.UPDATE) setTimeout(() => { bot.oracleV2.data.updateCacheFees()}, 3000);
        if(bot.mode == macro.BOT_MODE.MEV)    setTimeout(() => { bot.oracleV2.data.updateCacheMev();}, 3000);
        //if(bot.mode == macro.BOT_MODE.PAIR)   setTimeout(() => { bot.oracleV2.data.checkBadPair();}, 5000);
        if(bot.mode == macro.BOT_MODE.SKIM)   this.skim();
        if(bot.chain == macro.CHAIN.CUBE) PumpCube.init();
        if(bot.chain == macro.CHAIN.DFK) PumpDfk.init();

        //自动冲gas
        if(bot.config.feedAutoTimeHour > -1 && bot.mode !== macro.BOT_MODE.UPDATE && bot.mode !== macro.BOT_MODE.LOCAL){
            setInterval(() => {
                bot.client.bull.batchFeed();
            }, bot.config.feedAutoTimeHour * 60 * 60 * 1000);

            if(bot.config.junk.active) bot.client.junk.init();
        }
        Lazy.ins().logTs1("[bot] boot finish");
    }
    //TODO: 
    static checkGoldenCfg(){
        this.client.iBot
    }
    
    static provider(){
        return this.handle.wss[0].provider as ethers.providers.WebSocketProvider;
    }

    static _botOwner = "";
    static async getbotOwner(){
        if(this._botOwner == "") {
            const iBot = new ethers.utils.Interface(macro.abi.bot);
            const raw = await this.provider().call({
                to : this.config.bot.address,
                data : iBot.encodeFunctionData("owner", [])
            });
            this._botOwner = iBot.decodeFunctionResult("owner", raw)[0] as string;
        }
        return this._botOwner;
    }

    static getConfig(chain : macro.CHAIN){
        let config : Config;
        switch(chain){
            case macro.CHAIN.ONE: config = new ConfigOne(); break;
            case macro.CHAIN.OEC : config = new ConfigOec(); break;
            case macro.CHAIN.HECO : config = new ConfigHeco(); break;
            case macro.CHAIN.POLYGON : config = new ConfigPolygon(); break;
            case macro.CHAIN.FTM : config = new ConfigFtm(); break;
            case macro.CHAIN.CELO : config = new ConfigCelo(); break;
            case macro.CHAIN.HOO : config = new ConfigHoo(); break;
            case macro.CHAIN.XDAI : config = new ConfigXdai(); break;
            case macro.CHAIN.ROSE: config = new ConfigRose(); break;
            case macro.CHAIN.AURORA: config = new ConfigAurora(); break;
            case macro.CHAIN.KCC : config = new ConfigKcc(); break;
            case macro.CHAIN.VLX : config = new ConfigVlx(); break;
            case macro.CHAIN.BCH : config = new ConfigBch(); break;
            case macro.CHAIN.ASTR: config = new ConfigAstr(); break;
            case macro.CHAIN.WAN : config = new ConfigWan(); break;
            case macro.CHAIN.MILKADA: config = new ConfigMilkada(); break;
            case macro.CHAIN.SYS: config = new ConfigSys(); break;  //public 目前HK
            case macro.CHAIN.AVAX: config = new ConfigAvax(); break;
            case macro.CHAIN.KAI: config = new ConfigKai();break;   //public:新加坡
            case macro.CHAIN.TOMO: config = new ConfigTomo();break; //public:美国
            case macro.CHAIN.MOVR: config = new ConfigMovr();break; //public:de
            case macro.CHAIN.GLMR: config = new ConfigGlmr();break; 
            case macro.CHAIN.CUBE: config = new ConfigCube();break; //
            case macro.CHAIN.DFK:  config = new ConfigDfk();break;  //
            case macro.CHAIN.DOGE: config = new ConfigDoge();break;
            case macro.CHAIN.ETHW: config = new ConfigEthw();break;
            case macro.CHAIN.FITFI: config = new ConfigFitfi();break;
            case macro.CHAIN.GATE: config = new ConfigGate();break;
            case macro.CHAIN.CRO:  config = new ConfigCro();break;
            case macro.CHAIN.CANTO:  config = new ConfigCanto();break;
            case macro.CHAIN.POM:  config = new ConfigPom();break;
            case macro.CHAIN.KLAY: config = new ConfigKlay();break;
            case macro.CHAIN.SAMA: config = new ConfigSama();break;
            case macro.CHAIN.REI: config = new ConfigRei();break;
            case macro.CHAIN.KUB: config = new ConfigKub();break;
            case macro.CHAIN.BSC: config = new ConfigBsc();break;
            case macro.CHAIN.CORE: config = new ConfigCore();break;
            case macro.CHAIN.LAT: config = new ConfigLat();break;
            case macro.CHAIN.VS: config = new ConfigVs();break;
            case macro.CHAIN.ARB: config = new ConfigArb();break;
            case macro.CHAIN.NOVA: config = new ConfigNova();break;
            case macro.CHAIN.ZK: config = new ConfigZk();break;
            case macro.CHAIN.METIS: config = new ConfigMetis();break;
            case macro.CHAIN.PLS: config = new ConfigPls(); break;
            case macro.CHAIN.KAVA: config = new ConfigKava(); break;
            case macro.CHAIN.BASE: config = new ConfigBase(); break;
            case macro.CHAIN.OPBNB: config = new ConfigOpbnb(); break;
            case macro.CHAIN.WEMIX: config = new ConfigWemix(); break;
            case macro.CHAIN.MXC : config = new ConfigMxc(); break;
            case macro.CHAIN.ELA : config = new ConfigEla(); break;
            case macro.CHAIN.ZETA: config = new ConfigZeta(); break;
            case macro.CHAIN.OP: config = new ConfigOp(); break;
            case macro.CHAIN.DEGEN: config = new ConfigDegen(); break;
            case macro.CHAIN.BEVM: config = new ConfigBevm(); break;
            case macro.CHAIN.X: config = new ConfigX(); break;
            case macro.CHAIN.SEI: config = new ConfigSei(); break;
            case macro.CHAIN.S : config = new ConfigS(); break;
            case macro.CHAIN.ABS : config = new ConfigAbs(); break;
            default : throw(`#### ERROR CHAIN ID: ${chain}`); break;
        }
        return config;
    }

    static token(address:string){
        return this.oracleV2.data.pm.tokens.get(address);
    }

    static tokenFromName(name:string){
        for(let [k,v] of Object.entries(bot.config.tokens)){
            if(v.name == name || bot.token(k).symbol == name){
                //console.log(this.token(v.address));
                return this.token(k);
            }
        }
        throw(` not active token name: ${name}`);
    }

    static receiptParser(receipt:ethers.providers.TransactionReceipt, tokenAddress:string, usdtAddress:string, addGas = false, silences = false){
        let usdt = BigNumber.from('0');
        let token = BigNumber.from('0');
        //console.log(JSON.stringify(receipt));

        if(receipt && receipt.logs){
            //低级方法，建议使用interface解析transferevent https://github.com/ethers-io/ethers.js/issues/487
            for(let r of receipt.logs){ //参考sellReceipt的格式
                if(r.data.length <= 66
                    && !r.data.includes("0xff")
                    && r.topics[0] == "0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef"){ //transfer函数
                    //transfer事件
                    if(r.address == usdtAddress){
                        const bn : BigNumber = this.abiCoder.decode(["uint256"], r.data)[0]; //bignumber
                        usdt = usdt.add(bn);
                    }
                    if(r.address == tokenAddress){
                        const bn : BigNumber = this.abiCoder.decode(["uint256"], r.data)[0]; //bignumber
                        token = token.add(bn);
                    }
                }
            }
        } else {
            console.warn(" [receiptParser] receipt error");
        }
        const bUsdt = bot.token(usdtAddress);
        const bToken = bot.token(tokenAddress);
        const { COLOR: LINUX_COLOR } = macro;

        const tokenGas = bot.token(bot.config.eth);
        let gasCostBn = receipt.effectiveGasPrice ? receipt.gasUsed.mul(receipt.effectiveGasPrice) : macro.bn.zero;
        const gasCostNum = Number(utils.formatUnits(gasCostBn, tokenGas.decimals));

        const res = {
            usdt: {
                bn : usdt,
                num : bUsdt.toNum(usdt)
            },
            token : {
                bn : token,
                num : bToken.toNum(token)
            },
            tokenAddr:tokenAddress,
            usdtAddr:usdtAddress
        }
        if(addGas){
            res.usdt.bn = res.usdt.bn.add(gasCostBn);
            res.usdt.num = res.usdt.num + gasCostNum;
        }
        //console.log({usdt: res.usdt.num, token : res.token.num});
        if(!silences) console.log(`${LINUX_COLOR.Yellow} (${bUsdt.symbol}) ${res.usdt.num} - (${bToken.symbol}) ${res.token.num}${LINUX_COLOR.Off} gas:(${tokenGas.symbol}) ${gasCostNum.toFixed(4)} $${(gasCostNum*tokenGas.price).toFixed(4)}`);
        return res;
    }

    static receiptBlockNum(receipt: ethers.providers.TransactionReceipt, str:string){
        const blockNumber = receipt.blockNumber;
        const index = receipt.transactionIndex;
        Lazy.ins().log1(`${macro.COLOR.Purple} ${str} tx:${receipt.transactionHash} ${blockNumber} (${index})${macro.COLOR.Off}`);
    }

    static newContract(address:string, abi:any, wallet:ethers.Wallet, websocketId=0){
        return this.handle.wss[websocketId].contract(address, abi, wallet);
    }

    static newWallet(privateKey : string, websocketId=0){
        return this.handle.wss[websocketId].wallet(privateKey);
    }

    static fixAddress(){
        bot.config.eth = bot.config.eth.toLowerCase();
        bot.config.stableTokens = bot.config.stableTokens.map(x=> x = x.toLowerCase());
        bot.config.gasWatch = bot.config.gasWatch.map(x=> x = x.toLowerCase()); 

        bot.config.blackList = bot.config.blackList.map(x=> x = x.toLowerCase()); //TODO: 去除一般blacklist
        
        // 导入现有的黑名单数据到新的黑名单实例
        bot.blackList.import({ 
            permanent: bot.config.blackListPump.map(x => x.toLowerCase()),
            //temp: bot.config.blackList // 将一般黑名单作为临时黑名单导入
        });

        bot.config.bot.address = bot.config.bot.address.toLowerCase();
        bot.config.bot2.address = bot.config.bot2.address.toLowerCase();

        this._fixKey(bot.config.whiteListPair);
        this._fixKey(bot.config.routers);
        this._fixKey(bot.config.tokens);
        this._fixKey(bot.config.tokenBlackList);

        console.log("----- fixAddress -----");
        //console.log(bot.config);
    }

    private static _fixKey(obj:{[k:string]:any} = {}){
        for(let [k,v] of Object.entries(obj)){
            let newK = k.toLowerCase();
            if(newK != k){
                //if(bot.mode != macro.BOT_MODE.LOCAL) console.log("fixing address to Checksum Address...")
                obj[newK] = v;
                if(v["eq"]) v["eq"] = (v["eq"] as string[]).map(e=>e.toLocaleLowerCase());
                if(v["stable"]) v["stable"] = v["stable"].toLocaleLowerCase();
                if(v["eth"]) v["eth"] = v["eth"].toLocaleLowerCase();
                delete obj[k];
            }
        }
    }

    static async skim(){
        console.log("skim begin");
        //bc25cf77
        const total = bot.oracleV2.data.pm.data.size;
        let canSkim = 0;
        let badPair = 0;
        let i = 0;
        const masterAdr = await bot.client.bull.getOperator().ins().owner();

        for(const [addr, pair] of bot.oracleV2.data.pm.data){
            i++;
            try {
                //检查balance和resvers是否相等
                const [reserve0, reserve1] = await pair.getReservesServer();
                const {token0, token1} = pair;
                const balance0 = await this.getBalance(token0, pair.address);
                const balance1 = await this.getBalance(token1, pair.address);
                if(balance0.gt(reserve0) || balance1.gt(reserve1)){
                    console.log(`[${i}/${total}] ${macro.COLOR.BGreen}${addr} ${pair.token0Symbol}-${pair.token1Symbol}${macro.COLOR.Off}`);
                    if(balance0.gt(reserve0)) console.log(`${utils.formatEther(balance0.sub(reserve0))}`);
                    if(balance1.gt(reserve1)) console.log(`${utils.formatEther(balance1.sub(reserve1))}`);
                    await this._skim(pair.address, masterAdr);
                    console.log("skim success");

                    canSkim++;
                } else if (balance0.lt(reserve0) || balance1.lt(reserve1)){
                    console.log(`[${i}/${total}] ${macro.COLOR.Red}${addr} ${pair.token0Symbol}-${pair.token1Symbol}${macro.COLOR.Off}`);
                    badPair++;
                } else {
                    console.log(`[${i}/${total}] ${macro.COLOR.BBlack}${addr} ${pair.token0Symbol}-${pair.token1Symbol}${macro.COLOR.Off}`);
                }
            } catch(e){
                console.log(e);
                //console.log("error: ");
                console.log(`[${i}/${total}] ${macro.COLOR.Red}${addr} ${pair.token0Symbol}-${pair.token1Symbol}${macro.COLOR.Off}`);
            }
        }
        console.log(`skim end, ${canSkim}/${badPair}/${total}`);
    }

    static async _skim(addr:string, receive:string){
        const op = bot.client.bull.operators[0].ins();
        const nonce = await bot.provider().getTransactionCount(op.address);
        let contract = bot.newContract(addr, macro.abi.pair, op).ins();
        let res = await contract.functions.skim(receive, {gasLimit:1000000, nonce:nonce});
        await res.wait();
    }

    
    static async getBalance(token:string, holder:string){
        //0x70a08231
        const abiCoder = new ethers.utils.AbiCoder();
        const abi = ["function balanceOf(address account)"];
        const ifaec = new utils.Interface(abi);
        let res = await bot.provider().call({
            to : token,
            data : ifaec.encodeFunctionData("balanceOf", [holder])
        });
    
        //console.log(res);
        const bn :BigNumber = abiCoder.decode(["uint256"], res)[0];
        //console.log(bn);
        return bn;
    }

    static swapDataToWhale(
        from?:string,
        data? : SwapResult,
        beforeWhale = false,
        isEth=false,
        spender = macro.zeroAddress,
    ){
        if(data){
            let spenderToken = macro.zeroAddress;
            let spenderAmount = macro.bn.zero;
            if(spender !== macro.zeroAddress){
                spenderToken = data.path[0];
                spenderAmount = data.amountIn;
            }
            if(beforeWhale){
                //before
                const beforeToken =  data.path[0];
                return new WhaleSwapData(from, beforeToken, data.amountIn, isEth && beforeToken == bot.config.eth, undefined, spender, spenderToken, spenderAmount);
            } else {
                //after
                const afterToken = data.path[data.path.length-1];
                return new WhaleSwapData(data.to, afterToken, data.amountOut, isEth && afterToken == bot.config.eth, undefined, spender, spenderToken, spenderAmount);
            }
        } else {
            return new WhaleSwapData();
        }
    }

    static getLogPath(fileName="log.txt"){
        return `./src/data/${this.chain}/${fileName}`;
    }

    /**
     * 根据地址算出钱包地址，保证针对同个whale使用相同的操作员
     * @param whaleAddress 
     */
    static getOperatorIndex(whaleAddress:string, blockNum = 0, token=""){
        return (tools.valueOfStr(whaleAddress+token) + blockNum) % bot.config.maxOperator;
    }

    static getNextOperator(index:number, delta=1){
        return (index + delta) % bot.config.maxOperator;
    }

    static getPreOperator(index:number){
        return Math.abs(index-1) % bot.config.maxOperator;
    }

    static initClient(wallets:{[k:string]:string}, addr:string){
        const ops : Array<Array<ExContract>> = [];
        for(let i = 0; i < bot.config.mainnet.wss.length; i++){
            const contracts = [];
            for(const key of Object.values(wallets)){
                const wallet = bot.newWallet(key, i);
                contracts.push(bot.newContract(addr, macro.abi.bot, wallet.ins(), i));
            }
            ops.push(contracts);
        }
        return ops;
    }

}