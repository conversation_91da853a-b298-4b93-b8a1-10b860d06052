import { BigNumber, ethers, utils } from "ethers";
import bot from "../bot";
import BotPump from "./botPump";
import DataType, { FindPathController, FindPathResult, MixGas, ServerPumpData } from "./type/DataType";
import Lazy from "./lazy/LazyController";
import { macro } from "./macro";
import { PairExtend } from "./type/Pair";
import tools from "./tools";
import MevDetails from "./comp/MevDetails";

class PairStatus {
    reserve0 = 0; //更新前
    reserve1 = 0;
    update0 = 0; //更新后
    update1 = 0;
}

export default class BotTrash extends BotPump {
    changedPairs : Map<string, PairStatus> = new Map();
    bidMinUsd = 0.01;

    txBidCache  = new Map<number, {
        data : {
            totalGas: number;
            totalRewardValue: number;
            data: ServerPumpData[];
            encodePackedData: string;
        },
        mixGas: MixGas,
        opIndex: number,
        rewards : number,
        nonce : number,
    }>(); //记录需要抢先交易的tx，如果有更高的gas交易发生就发送更高的gas交易

    details = new MevDetails();
    total_expected_reward = 0;


    constructor(wallets:{[k:string]:string}, addr:string){
        super(wallets, addr);
        if(bot.config.trash.rewardMin) this.rewardMinUsd = bot.config.trash.rewardMin;
        if(bot.config.trash.delay > 0) bot.event.on(macro.EVENT.WS_ON_BLOCK, this.onBlock.bind(this));
        if(bot.config.trash.bidMinUsd) this.bidMinUsd = bot.config.trash.bidMinUsd;
    }

    onChange(address:string,r0:number,r1:number, onlyUpdateCache = false){
        let c = this.changedPairs.get(address);
        if(c){
            c.update0 = r0;
            c.update1 = r1;
        } else {
            const {reserve0, reserve1} = bot.oracleV2.getPairByAddr(address);
            this.changedPairs.set(address, {
                reserve0 : reserve0,
                reserve1 : reserve1,
                update0 : r0,
                update1 : r1
            });
        }
        if(bot.config.trash.delay == 0 && !onlyUpdateCache) this.doTrash();
    }

    private onBlock(num : number){
        // 当区块号变化时，缓存会在下次调用 updateCache() 时自动清理
        if(bot.config.trash.delay > 0) {
            setTimeout(() => { this.doTrash()}, bot.config.trash.delay);
        }
    }

    private clean(){
        this.changedPairs.clear();
    }


    private doTrash(){
        if(this.changedPairs.size == 0) return;
        const isBlindBid = (bot.config.trash.l2 && bot.config.trash.l2.bid) ? true : false;
        //console.log("isBlindBid: ", isBlindBid);
        /*
        if(bot.chain == macro.CHAIN.PLS && bot.handle.minGas() > utils.parseUnits('1500000', 'gwei')){
            Lazy.ins().logTs1(`${macro.COLOR.BRed}minGas too high${macro.COLOR.Off}`);
            this.clean();
            return;
        }
        */

        if(isBlindBid){
            this._trashL2BlinkBid();
        } else {
            this._trash();
        }
    }

    private async _trash(layer=1, opIndex =-1 ){

        let controller = new FindPathController();
        //let results = new PumpData_old();
        
        let mixGas : MixGas;

        switch(bot.chain){
            case macro.CHAIN.ARB:
                mixGas = new MixGas();
                mixGas.maxPriorityFeePerGas = utils.parseUnits('1','wei');
                mixGas.maxFeePerGas = utils.parseUnits('0.1','gwei');
                break;
            case macro.CHAIN.NOVA:
                mixGas = new MixGas();
                mixGas.maxPriorityFeePerGas = utils.parseUnits('1','wei');
                mixGas.maxFeePerGas = utils.parseUnits('0.01','gwei');
                break;
            case macro.CHAIN.BASE:
                mixGas = new MixGas();
                mixGas.maxPriorityFeePerGas = utils.parseUnits('0.001','gwei');
                mixGas.maxFeePerGas = utils.parseUnits('0.001','gwei');
                break;
            case macro.CHAIN.METIS:
                mixGas = new MixGas();
                break;
            case macro.CHAIN.OPBNB:
                mixGas = new MixGas();
                mixGas.maxPriorityFeePerGas = utils.parseUnits('0.000001','gwei');
                mixGas.maxFeePerGas = utils.parseUnits('0.000001','gwei');
                break;
            case macro.CHAIN.DEGEN:
                mixGas = new MixGas().bigMore();
            default:
                mixGas = bot.config.trash.bid ? bot.handle.highestMixGas.clone().more() : (new MixGas()).more();
                break;
        }

        if(bot.config.trash.gasMin !== undefined){
            let minGas = utils.parseUnits(bot.config.trash.gasMin.toString(), 'gwei');
            if(mixGas.value().lt(minGas)){
                mixGas.set(minGas);
            }
        }
        
        mixGas.setGasLimit(macro.abi.botPumpGasSingleRouter);

        const pairCache : {[addr:string]:PairExtend} = {}
        //所有有变动的pair加入到缓存
        this.changedPairs.forEach((status, address)=>{
            const pair = bot.oracleV2.getPairByAddr(address).clone();
            pair.reserve0 = status.update0;
            pair.reserve1 = status.update1;
            pairCache[address] = pair;
        });

        this.changedPairs.forEach((status, address)=>{
            let pair = bot.oracleV2.getPairByAddr(address);
            if(pair.status != macro.PAIR_STATUS.ACTIVE){
                return;
            }
            const {reserve0, reserve1, update0, update1} = status;
            let inToken = ((reserve0 / reserve1) < (update0 / update1)) ? pair.token0 : pair.token1;
            if(layer == 1){
                Lazy.ins().logTs1(`${bot.handle.blockNum} ${macro.COLOR.BBlack}${address}${macro.COLOR.Off} ` 
                + `[${inToken == pair.token0 ? "*":""}${pair.token0Symbol} /`
                + ` ${inToken == pair.token1 ? "*":""}${pair.token1Symbol}] `
                + `${macro.COLOR.BBlack} r0:${pair.reserve0.toFixed(5)} r1:${pair.reserve1.toFixed(5)}${macro.COLOR.Off}`);
            } else {
                Lazy.ins().logTs1(`${macro.COLOR.BBlack}         ${address} `
                + `[${inToken == pair.token0 ? "*":""}${pair.token0Symbol} /`
                + ` ${inToken == pair.token1 ? "*":""}${pair.token1Symbol}] `
                + `  r0:${pair.reserve0.toFixed(5)} r1:${pair.reserve1.toFixed(5)}${macro.COLOR.Off}`);
            }
            
            //this.trash(address, inToken);
            try {
                const r = this.findPath(address, inToken, mixGas, pairCache);
                controller.add(r);
            } catch(e){
                console.log("[findPath error] : ", e);
                return;
            }
        });
        //找到最大利润的pair
        if(controller.data().length == 0) {
            this.clean();
            return;
        }

        let top1 = controller.data()[0];
        if(opIndex == -1) opIndex = bot.getOperatorIndex(top1.pairs[0], bot.chain == macro.CHAIN.ARB || bot.chain == macro.CHAIN.METIS ? ~~(bot.handle.blockNum / 3) : bot.handle.blockNum+layer);

        controller.sort();

        if(bot.config.eoaRouter.active && bot.oracleV2.data.pm.get(top1.pairs[0]).version == macro.PAIR_VERSION.V2_EOA){
            bot.client.oneRouter.onTrash(top1.amount, top1.tokenOuts, mixGas);
            this.clean();
            return;
        }

        //竞价
        if((bot.config.trash.bid && controller.totalRewardValue > this.bidMinUsd && layer == 1) || bot.chain == macro.CHAIN.HECO){
            await this.batchTrash([top1], mixGas, opIndex, true);
            if(controller.data().length > 1){
                await this.batchTrash(controller.data().slice(1), mixGas, bot.getNextOperator(opIndex));
                opIndex = bot.getNextOperator(opIndex, 2);
            }
        } else {
            this.batchTrash(controller.data(), mixGas, opIndex, false, undefined, bot.chain !== macro.CHAIN.X);
            opIndex = bot.getNextOperator(opIndex, 1);
        }

        //一层套利结束
        this.clean();
        if(layer == 1) bot.client.safe.beginPumps(mixGas);

        //进行二层套利
        if(layer > 1 || controller.data().length == 0) {
            this.clean();
            return;
        }

        controller.data().forEach( p => {
            for(let i = 0; i < p.pairs.length; i++){
                const pair = bot.oracleV2.getPairByAddr(p.pairs[i]).clone();
                pair.updateBalance(p.tokenOuts[i], p.amounts[i], p.amounts[i+1]);
                this.onChange(pair.address, pair.reserve0, pair.reserve1, true);
            }
        });

        this._trash(2, opIndex); //递归第二层
        
    }

    private async _trashL2BlinkBid(layer=1, opIndex=-1) {
        let results : FindPathResult[] = [];
        //所有有变动的pair加入到缓存
        const pairCache : {[addr:string]:PairExtend} = {}
        this.changedPairs.forEach((status, address)=>{
            const pair = bot.oracleV2.getPairByAddr(address).clone();
            pair.reserve0 = status.update0;
            pair.reserve1 = status.update1;
            pairCache[address] = pair;
        });

        this.changedPairs.forEach((status, address)=>{
            let pair = bot.oracleV2.getPairByAddr(address);
            if(pair.status != macro.PAIR_STATUS.ACTIVE) return;
            const {reserve0, reserve1, update0, update1} = status;
            let inToken = ((reserve0 / reserve1) < (update0 / update1)) ? pair.token0 : pair.token1;
            if(layer == 1){
                Lazy.ins().logTs1(`${bot.handle.blockNum} ${macro.COLOR.BBlack}${address}${macro.COLOR.Off} ` 
                + `[${inToken == pair.token0 ? "*":""}${pair.token0Symbol} /`
                + ` ${inToken == pair.token1 ? "*":""}${pair.token1Symbol}] `
                + `${macro.COLOR.BBlack} r0:${pair.reserve0.toFixed(5)} r1:${pair.reserve1.toFixed(5)}${macro.COLOR.Off}`);
            } else {
                Lazy.ins().logTs1(`${macro.COLOR.BBlack}         ${address} `
                + `[${inToken == pair.token0 ? "*":""}${pair.token0Symbol} /`
                + ` ${inToken == pair.token1 ? "*":""}${pair.token1Symbol}] `
                + `  r0:${pair.reserve0.toFixed(5)} r1:${pair.reserve1.toFixed(5)}${macro.COLOR.Off}`);
            }
            const r = this._findPathBlindBid_old(address, inToken, pairCache, 6, bot.config.trash.l2?.payout);
            if(r.length > 0) results = results.concat(r);
        });

        if(opIndex == -1) opIndex = bot.getOperatorIndex(macro.zeroAddress, bot.handle.blockNum+layer);
        //if(results.length > 0) console.log(`results length: ${results.length}`);

        for(const result of results){
            const mixGas = new MixGas();
            mixGas.set(result.maxFeePerGas);
            mixGas.setGasLimit(result.gasLimit.add(BigNumber.from(100000)));
            await this.batchTrash([result], mixGas, opIndex, false);
            opIndex = bot.getNextOperator(opIndex, 1);
        }
        //一层套利结束
        this.clean();
        
        //进行二层套利
        if(layer > 1 || results.length == 0) return;
        for(const result of results){
            for(let i = 0; i < result.pairs.length; i++){
                const pair = bot.oracleV2.getPairByAddr(result.pairs[i]).clone();
                pair.updateBalance(result.tokenOuts[i], result.amounts[i], result.amounts[i+1]);
                this.onChange(pair.address, pair.reserve0, pair.reserve1, true);
            }
        }
        this._trashL2BlinkBid(2, opIndex);
        
    }

    async batchTrash(paths:FindPathResult[], mixGas:MixGas, opIndex:number, bid=false, customNonce?:number, estimateReward=true, from = "trash"){
        //if(bot.config.junk.active) return;
        let filteredPaths : FindPathResult[];
        if(bot.config.filterCachedPaths && from == "trash"){
            // 应用缓存机制，过滤掉已执行的路径
            filteredPaths = this.details.filterCachedPaths(paths);

            // 如果所有路径都被缓存过，直接返回
            if (filteredPaths.length === 0) {
                Lazy.ins().logTs(`${macro.COLOR.Yellow}[${from}] All paths cached, skipping execution${macro.COLOR.Off}`);
                return;
            }
        } else {
            filteredPaths = paths;
        }


        let s = DataType.findPathResultsToServerPumpData(filteredPaths);
        Lazy.ins().logTs(`${from=="trash" ? macro.COLOR.BBlue : macro.COLOR.Purple}[${from}] ${mixGas.display()} len:${filteredPaths.length}/${paths.length}  $ ${s.totalRewardValue.toFixed(5)} ${bid?"#bid":""} ${macro.COLOR.Off}`);
        const opAddr = this.operators[opIndex].ins().address;

        //预计算收益 && bot.config.trash.delay > 0
        if(estimateReward && bot.config.trash.active){
            let res = await bot.provider().call({
                from: opAddr,
                to : bot.config.bot.address,
                data : s.encodePackedData,
            });
            let result = bot.contractIns.iBot.decodeFunctionResult("pumpSmart", res);
            let rewards = result[1] as BigNumber[];

            // 提取出不是0的pair
            const validIndices = rewards.map((r, index) => ({ r, index }))
                .filter(item => !item.r.isZero())
                .map(item => item.index);
            
            if (validIndices.length > 0) {
                const validPaths = validIndices.map(index => filteredPaths[index]);
                const validData = s.data.filter((_, index) => validIndices.includes(index));

                // 重新计算总收益和生成新的编码数据
                const newServerData = DataType.findPathResultsToServerPumpData(validPaths);
                Lazy.ins().logTs(`${macro.COLOR.Green}Filtered trash rewards from ${s.data.length} to ${validData.length} paths, total value: ${newServerData.totalRewardValue.toFixed(5)}${macro.COLOR.Off}`);
                // 使用新的有效数据替换原来的数据
                s = newServerData;
            } else {
                Lazy.ins().logTs(`${macro.COLOR.Red}Trash rewards zero${macro.COLOR.Off}`);
                return;
            }
        }


        const opNonce = bot.handle.nonce.get(opAddr);
        //if(!bot.config.pump.active) bot.handle.nonce.update(opAddr, false, bot.config.blockTime);

        if(bid){
            this.txBidCache.set(opIndex, {data:s, mixGas:mixGas, opIndex : opIndex, rewards:s.totalRewardValue, nonce: opNonce});
            //10个块后清除bid信息
            setTimeout(()=>{
                this.txBidCache.delete(opIndex);
            }, bot.config.blockTime * 1000 * 7); 
        }
        this.pumpTrash(s, mixGas, opIndex, s.totalRewardValue, customNonce);
    }

    async pumpTrash(s:{
        totalGas: number;
        totalRewardValue: number;
        data: ServerPumpData[];
        encodePackedData: string;
    }, mixGas:MixGas, opIndex : number, rewards : number, nonce?:number, tryAgain=true){
        if(bot.mode == macro.BOT_MODE.DEBUG || bot.mode == macro.BOT_MODE.LOCAL || bot.mode == macro.BOT_MODE.TEST) return;

        if(nonce!=0 && !nonce){
            const op = this.operators[opIndex].ins();
            nonce = bot.handle.nonce.useSync(op.address); 
        }

        //签名
        //let gasLimit = bot.chain == macro.CHAIN.ZETA ? s.totalGas + 300000 : Math.max(macro.abi.botPumpGasLimit, s.totalGas + 300000); //mxc不够钱
        let gasLimit = s.totalGas + 400000; //mxc不够钱
        let signData = this.signTxSync(opIndex, s.encodePackedData, mixGas,  gasLimit, bot.config.bot.address, nonce);


        bot.handle.sendTransactionAll(signData, undefined,
            (receipt) => {
                if(bot.chain == macro.CHAIN.X){
                    //x-layer如果立刻更新会有bug，延迟更新
                    bot.handle.nonce.update(receipt.from, false, 2);
                } else {
                    bot.handle.nonce.update(receipt.from);
                }
                //有tran的是pump
                this.logTrash(receipt, s.data, opIndex, rewards);
            },
            (pendingE)=>{
                //console.log(pendingE);
                if(pendingE.body){
                    Lazy.ins().log1(`${Object.values(pendingE.body).join("")}`);
                } else if(pendingE.response){
                    Lazy.ins().log1(`${pendingE.response}${macro.COLOR.Off}`);
                }
                Lazy.ins().log1(`${macro.COLOR.Red}timeout or replaced${macro.COLOR.Off}`);
                if(bot.chain == macro.CHAIN.DEGEN && tryAgain){
                    Lazy.ins().log1(`${macro.COLOR.Red}try again${macro.COLOR.Off}`);
                    let gas = mixGas.clone().lessMore();
                    this.pumpTrash(s, gas, opIndex, rewards, nonce, false);
                } else if(bot.chain == macro.CHAIN.X){
                    const op = this.operators[opIndex].ins();
                    bot.handle.nonce.update(op.address, true, 1);
                }
                bot.handle.wss.forEach((w)=> w.updateMinGas());
            },
            (tx:string)=>{
                //打包完成但是出错了 //pump
                this.txBidCache.delete(opIndex);
                this.logFail(s, tx);
            }
        );
    }

    //maxPay 0.5 = 50% 给50的利润给gas
    _findPathBlindBid_old(pairAddr:string, inToken:string, pairCache : {[addr:string]:PairExtend}, pathNum = 6, maxPayout = 0.5){
        let pair = bot.oracleV2.getPairByAddr(pairAddr);
        if(pair.mevPath.length == 0) return [];
        let result : FindPathResult[] = [];

        let rewards = 0;
        const reverse = inToken == pair.token0;
        for(let i = 0 ; i < Math.min(pair.mevPath.length, pathNum); i++){
            let mev = pair.mevPath[i];
            let newPairs = [...mev.pairs];

            let fees : number[];
            if(reverse){
                newPairs.reverse();
                fees = mev.fees1;
            } else {
                fees = mev.fees0;
            }
            let stable = reverse ? bot.token(mev.s1) : bot.token(mev.s0);
            const pairs_ref = newPairs.map((addr)=>{ return pairCache[addr] || bot.oracleV2.getPairByAddr(addr)});
            const isBad = reverse ? mev.status1 == macro.MEV_STATUS.DISABLE : mev.status0 == macro.MEV_STATUS.DISABLE;

            let amountNum = mev.type == macro.MEV_TYPE.BASE ? this.findMaxGolden(stable.address, pairs_ref, []) : this.findMax(stable.address, pairs_ref, []);
            if(amountNum > 0){
                let calc = this._getAmountsOut_old(stable.address, amountNum, newPairs, pairCache);
                const bEth = bot.token(bot.config.eth);
        
                //计算如果出让maxPayOut % 的利润，对应的mixGas是多少
                let l1Fee = this._calcL1Fees(newPairs.length);
                let l1FeeUsd = bEth.toNum(l1Fee) * bEth.price;
                let payoutUsd = (calc.reward * stable.price - l1FeeUsd) * maxPayout;

                if(payoutUsd < 0) return [];

                const gasCostEth = bEth.toBigNumber(payoutUsd / bEth.price);
                const gasLimit =  BigNumber.from(mev.gas + 20000);
                let maxFeePerGas = gasCostEth.div(gasLimit);
                
                //计算利润是否大于 maxPay * bot.config.gasMin * gasLimit 的价值
                const min = utils.parseUnits(bot.config.gasMin.toString(), 'gwei');
                const max = utils.parseUnits(bot.config.gasMax.toString(), 'gwei');
                if(maxFeePerGas.gt(min)){
                    if(maxFeePerGas.gt(max)) maxFeePerGas = max;
                    let printStr = "";  
                    if(!isBad){
                        const gasCostStable = stable.toBigNumber(payoutUsd / stable.price);
                        result.push({
                            convertEth : mev.convertEth,
                            gas : mev.gas,
                            fees : fees,
                            mevType : mev.type,
                            tokenIn0or1 : reverse ? mev.tokenIn0or1[1] : mev.tokenIn0or1[0],

                            pairs : newPairs,
                            amount : stable.toBigNumber(Number(amountNum.toFixed(stable.decimals))),
                            reward : calc.reward,
                            rewardValue : calc.reward,
    
                            stable : stable.address,
                            gasCost : gasCostStable,
    
                            maxFeePerGas : maxFeePerGas, //for findPathBid
                            gasLimit : gasLimit,
    
                            amounts: calc.amounts,
                            tokenOuts : calc.tokenOuts
                        });
                        this._updateCache(calc.tokenOuts, calc.amounts, newPairs, pairCache);
                        rewards += calc.reward * stable.price;
                        printStr += macro.COLOR.Green;
                    } else {
                        printStr += macro.COLOR.Red;
                    }

                    for(let z = 0 ; z < calc.amounts.length; z++){
                        printStr += `${bot.token(calc.tokenOuts[z]).symbol}(${calc.amounts[z].toFixed(3)}) ${z ==  calc.amounts.length-1 ? "" : "-> "}`;
                    }
                    Lazy.ins().log1(`${printStr} +${calc.reward.toFixed(5)} ${macro.COLOR.BBlack} l1Gas:${(l1FeeUsd/bEth.price).toFixed(6)} g:${(payoutUsd/stable.price).toFixed(5)}${macro.COLOR.Off}`);
                    //Lazy.ins().log1(`maxFeePerGas: ${utils.formatUnits(maxFeePerGas, 'gwei')}, gasLimit:${gasLimit.toString()}`);
                } else {
                    Lazy.ins().log1(`amountNum : ${amountNum.toFixed(9)} maxFeePerGas: ${utils.formatUnits(maxFeePerGas, 'gwei')}, gasLimit:${gasLimit.toString()}`);
                }
            }
            
        }
        return result;
    }

    _getAmountsOut_old(tokenIn:string, amountIn:number, pairAddrs:string[], cache : {[addr:string]:PairExtend}= {}){
        const tokenOuts:string[] = [];
        const amounts = [amountIn];
        tokenOuts[0] = tokenIn;
        for(let i = 0 ; i < pairAddrs.length; i++){
            tokenIn = tokenOuts[i];
            const {token0, token1, reserve0, reserve1, fee0, fee1, version} = cache[pairAddrs[i]] || bot.oracleV2.getPairByAddr(pairAddrs[i]);
            tokenOuts[i+1] = tokenIn == token0 ? token1 : token0;
            const [reserveIn, reserveOut] = tokenIn == token0 ? [reserve0, reserve1] : [reserve1, reserve0];
            //amounts[i+1] = tools.getAmountOut(amounts[i], reserveIn, reserveOut);
            amounts[i+1] = tools.getAmountOut(amounts[i], reserveIn, reserveOut, tokenIn == token0 ? fee0 : fee1, version == macro.PAIR_VERSION.V2_STABLE);
        }
        let reward = amounts[amounts.length-1] - amounts[0];
        return {
            amounts : amounts, //amount1 -> amount2 -> amount3
            tokenOuts : tokenOuts, //token1 -> token2 -> token3
            reward : reward,
            sameDecimals : tokenOuts[tokenOuts.length-1] != tokenOuts[0],
        }
    }

    _updateCache(tokenOuts :string[], amounts:number[], pairAddrs:string[], cache : {[addr:string]:PairExtend}= {}){
        for(let i = 0 ; i < pairAddrs.length; i++){
            cache[pairAddrs[i]] ??= bot.oracleV2.getPairByAddr(pairAddrs[i]).clone();
            cache[pairAddrs[i]].updateBalance(tokenOuts[i], amounts[i], amounts[i+1]);
        }
    }

    _calcL1Fees(pairLength:number){
        //https://community.optimism.io/docs/developers/build/transaction-fees/
        /*
        let data_fees = 0;
        for(const d of data){
            data_fees += d=="0" ? 4 : 16;
        }
        data_fees = data_fees / 2;
        */
        const data_fees = 2051 + (pairLength - 2) * 412; //估算
        let l1_fees = bot.handle.l1GasPrice?.gasPirce.mul(BigNumber.from(data_fees + 188)).mul(BigNumber.from(1000)).div(BigNumber.from(684));
        if(!l1_fees) throw('error l1 fees');
        //console.log(utils.formatEther(l1_fee));
        return l1_fees;
    }

    onHigherGas(trans : ethers.providers.TransactionResponse){
        if(trans.to == bot.config.bot.address || trans.to == bot.config.bot2.address) return;
        const gas = trans.maxPriorityFeePerGas || trans.gasPrice || bot.handle.minGas();
        if(gas.gt(utils.parseUnits(bot.config.gasMax.toFixed(9), 'gwei'))) return; //reachMaxGas
        if(this.txBidCache.size == 0) return;

        for(let [k,v] of this.txBidCache.entries()){
            if(v.mixGas.value().gte(gas)) continue; //参与所有竞价
            Lazy.ins().log1(`op:(${k}/${this.txBidCache.size}) trash bid [${trans.from}] gas: ${utils.formatUnits(gas, 'gwei')}`);
            const v2 = {...v};
            v2.mixGas = v2.mixGas.litterMore(trans).clone();
            this.txBidCache.set(k, v2);
            this.pumpTrash(v2.data, v2.mixGas, v2.opIndex, v2.rewards, v2.nonce);
        }
    }

    async logTrash(receipt:ethers.providers.TransactionReceipt, serverData:ServerPumpData[], seed:number, expectRewards:number){
        if(!this.hashPool.enable(receipt.transactionHash)) return;

        //计算收益
        const { effectiveGasPrice, cumulativeGasUsed, gasUsed } = receipt;
        const bEth = bot.token(bot.config.eth);
        const costVal = bEth.toNum((effectiveGasPrice || cumulativeGasUsed).mul(gasUsed)) * bEth.price;

        const sum = {
            hash : receipt.transactionHash,
            block : receipt.blockNumber,
            index : receipt.transactionIndex,
            total : 0,
            success : 0,
            reward : 0
        };
        let results : number[] = [];
        let rewards : BigNumber[];

        const logs = (receipt as any)["logs"] as Array<any>;
        if(logs && logs.length > 0){
            let logData = logs[logs.length-1].data;
            [ results, rewards ]= bot.abiCoder.decode(["uint8[]","uint112[]"], logData);
            sum.total = results.length;
            for(let i = 0; i < serverData.length; i++){
                if(results[i] == macro.PUMP_RESULTS.Success || results[i] == macro.PUMP_RESULTS.SuccessOverlow){
                    sum.success++;
                    const pIn = bot.oracleV2.data.pm.get(serverData[i].pairs[0].addr);
                    const tokenIn = bot.token(serverData[i].tokenIn0or1.isZero() ? pIn.token0 : pIn.token1);
                    sum.reward += tokenIn.toNum(rewards[i]) * tokenIn.price;
                }
            }
        }

        this.details.total_cost += costVal;
        this.details.total_reward += sum.reward;
        this.total_expected_reward += expectRewards;
        this.details.updateUptime();
    
        let rewardStr = "";
        let str : string[] = [];
        if(sum.reward > 0){
            rewardStr = `$ ${sum.reward.toFixed(3)} - ${costVal.toFixed(3)} = ${(sum.reward-costVal).toFixed(3)} (${expectRewards.toFixed(3)}) ${results.join(",")} `;
            str.push(`$ ${sum.reward.toFixed(3)} - ${costVal.toFixed(3)} = ${(sum.reward-costVal).toFixed(3)} (${expectRewards.toFixed(3)})`);
            str.push(sum.index.toString());
        } else {
            rewardStr = `$  - ${costVal.toFixed(4)} (${expectRewards.toFixed(3)}) `;
            str.push(`$       - ${costVal.toFixed(4)} (${expectRewards.toFixed(3)})`);
            str.push(sum.index.toString());
        }
        bot.localLog.append(macro.FILE.LOG_TRASH, sum.hash, str);
        Lazy.ins().log(`${macro.COLOR.Blue} op:(${seed}) trash tx:${sum.hash} ${sum.block} (${sum.index}) `
                +`${sum.reward > 0 ? macro.COLOR.BBlue : ""}${rewardStr} ${macro.COLOR.Off}`);

        if(this.details.uptime % 10 * 60 == 0){
            Lazy.ins().log(`${macro.COLOR.UPurple}[trash] uptime: ${this.details.formatUpTime()} s, expect: $${this.total_expected_reward.toFixed(3)} cost: $${this.details.total_cost.toFixed(3)} reward : $${this.details.total_reward.toFixed(3)}`);
        }

    }
}