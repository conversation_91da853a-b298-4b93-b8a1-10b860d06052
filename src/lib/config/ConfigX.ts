import Config from "./Config";

export default class ConfigX extends Config {
    // https://xlayerrpc.okx.com
    // wss://xlayerws.okx.com

    // https://rpc.xlayer.tech
    // wss://ws.xlayer.tech

    mainnet = {
        data : "https://rpc.xlayer.tech",
        //data : "https://xlayerrpc.okx.com",
        wss : ["b|https://rpc.xlayer.tech", "x|wss://ws.xlayer.tech"],
        //wss : ["b|https://xlayerrpc.okx.com", "x|wss://xlayerws.okx.com"],
        //wss : ["https://rpc.xlayer.tech"],
    };
    blockTime = 5;
    eth = "******************************************";
    stableTokens = ["******************************************", "******************************************"];
    gasMin = 0.100001;
    gasMax = 100;
    gasDelta = 0.0001;
    feedAutoTimeHour = 0.5;

    feedMin = 0.3;
    feedMax = 0.35;
    feedPumpMulti = 1; //pump的填充gas百分比
    filterCachedPaths = true;

    maxOperator = 2;

    bot = {
        active:false,
        crazy:false,
        //address : "******************************************", //1115, logic:******************************************
        address : "******************************************", //20250818, logic:******************************************

    }
    pump = {active:false, gasMax:100, countMin:1, rewardMin: 0.000001, maxPump:15}
    trash = {active:true, bid:false, delay:0, rewardMin:0.000001}
    junk = {active:false, delay_ms: 10 * 1000, maxPair:4};

    tokens = {
        "******************************************" : {name:"wokb", ignore:true, isEth:true, price:200},
        "******************************************" : {name:"usdt", ignore:true, isEth:false, price:1},
    };

    routers = {
        "******************************************" : { name: "abs" },
        "******************************************" : { name: "okx", skipUpdate:true },
        "******************************************" : { name: "revo", fee:99800 }, //v2_factory:******************************************, v3:******************************************
        //"******************************************" : { name : "revo_smart" }, 
        "******************************************" : { name : "dyo", fee:99700 }, //代码里是99750
        "******************************************" : { name:"dyo2", fee:99700},
        //"******************************************" : { name : "dackie_smart" },
        "******************************************" : { name : "dackie", fee:99750 },
        //"******************************************" : { name : "izu" },

        //"******************************************" : { name: "savm2"}
        "0x881fB2f98c13d521009464e7D1CBf16E1b394e8E" : { name: "potato", fee:99750},
        "0x40A7F27998e480a80C1F7185a44b466aD1c43Fcd" : { name: "lfg"},
        //"0x69c236e021f5775b0d0328ded5eac708e3b869df" : { name:"okx"},
        //"0x2b59b462103efaa4d04e869d62985b43b46a93c9" : { name:"quick-lp"},
    };

    gasWatch = [
        "0xf1b5f1eD80e31edb60365C19E8B885E1A4e3ae3a"
    ]
    blackListPair = [];

    blackList = []

    blackListPump = []
}