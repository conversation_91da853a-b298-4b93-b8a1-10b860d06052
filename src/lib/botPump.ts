import { BigNumber, ethers, UnsignedTransaction, utils } from "ethers";
import bot from "../bot";
import DataType, { FindPathController, FindPathResult, FindPathResultsToServerPumpData, MixGas, ServerPairData, ServerPumpData } from './type/DataType';
import { ExWallet } from "./comp/EthersWrapper";
import { macro } from "./macro";
import { PairExtend } from "./type/Pair";
import tools from "./tools";
import HashPool from "./comp/HashPool";
import WasmSign from "./comp/WasmSign";
import Lazy from "./lazy/LazyController";
export default class BotPump {
    public operators : Array<ExWallet> = [];

    maxPumpDefault = 6;
    hashPool = new HashPool(50);
    failPool = new HashPool(40);

    public rewardMinUsd = 0.005; //单位usd
    public  amountInMin = 0.002; //最小的交易单位值


    constructor(wallets:{[k:string]:string}, addr:string){
        for(const key of Object.values(wallets)){
            const wallet = bot.newWallet(key);
            this.operators.push(wallet);
        }
        if(bot.config.pump.rewardMin) this.rewardMinUsd = bot.config.pump.rewardMin;
    }

    async simulateMev(pairAddr:string, tokenIn:string, amountIn:number, amountOut:number){
        pairAddr = pairAddr.toLocaleLowerCase();
        tokenIn = tokenIn.toLocaleLowerCase();
        const pair = bot.oracleV2.getPairByAddr(pairAddr);
        
        let mevs = bot.oracleV2.data.getMevPath(pairAddr);
        let res = await bot.contractIns.batchCheckMev(mevs);
        for(let k = 0 ; k < res.length; k++){
            const {fee0, fee1, gas} = res[k];
            mevs[k].fees0 = fee0;
            mevs[k].fees1 = fee1;
            mevs[k].gas   = bot.chain == macro.CHAIN.MXC ? gas + 150000 : gas + 60000; //每一条增加7万gas用于计划额外消耗
            mevs[k].status0 = mevs[k].fees0.length > 0 ? macro.MEV_STATUS.ACTIVE : macro.MEV_STATUS.DISABLE;
            mevs[k].status1 = mevs[k].fees1.length > 0 ? macro.MEV_STATUS.ACTIVE : macro.MEV_STATUS.DISABLE;
        }
        pair.mevPath = mevs;
        //console.log(pair.mevPath);
        //console.log("------- displayMevs ----------");
        //Helper.displayMevs(pair.mevPath);

        const tokenOut = pair.token0 == tokenIn ? pair.token1 : pair.token0;
        const pairCache : {[addr:string]:PairExtend} = {};
        const _p = pair.clone();
        _p.updateBalance(tokenIn, amountIn, amountOut);
        pairCache[_p.address] = _p;
        const mixGas = (new MixGas()).setGasLimit(macro.abi.botPumpGasSingleRouter).set(utils.parseUnits(bot.config.gasMin.toString(), 'gwei'));
        console.log('gas: ', mixGas.display());
        const r = this.findPath(pairAddr, tokenIn, mixGas, pairCache);

        let rewards = 0;
        r.forEach(_r => rewards += _r.reward);
        console.log("rewards: ", rewards);

        let s = DataType.findPathResultsToServerPumpData(r);
        console.log(s);
    }

    async mev(trans : ethers.providers.TransactionResponse, path : string[], amountsOut:number[], router:string, pairCache : {[addr:string]:PairExtend}, isEoa:boolean){
        if(!bot.oracleV2.isReady) return;
        
        //写轮眼, oneRouter模式不启用
        if(path[0] == path[path.length-1]){
            if(isEoa){
               //不pump其他机器人的eoa router
               return;
            } else if(bot.config.stableTokens.includes(path[0])){
                //todo: 有人使用router套利，价值大于$0.1，写轮之
                Lazy.ins().log(`${macro.COLOR.Yellow} sharingan swap${macro.COLOR.Off}`);
                bot.client.safe.sharingan(path, amountsOut, trans);
            }
        }

        if(Object.keys(pairCache).length == 0 ) return;

        let controller = new FindPathController();
        //let results = new PumpData_old();

        const mixGas = this.caclGas(trans); //mxc链使用最小gas

        for(let i = 0 ; i < path.length-1; i++){
            const lp = bot.oracleV2.getPair(router, path[i], path[i+1]);
            if(!lp) continue;
            const r = this.findPath(lp.address, path[i], mixGas, pairCache);
            controller.add(r);
        }
        if(controller.data().length == 0) return;
        let opIndex = bot.getOperatorIndex(trans.from || macro.zeroAddress, trans.nonce);

        let coCount = bot.config.pump.countMin;
        if(bot.chain == macro.CHAIN.PLS){
            if(isEoa){
                coCount = 1;
            //} else if(controller.totalRewardValue > 50) {
            //    coCount = 4; //大于50美元
            } else if(controller.totalRewardValue > 20) {
                coCount = 3; //大于13美元
            } else if(controller.totalRewardValue > 5) {
                coCount = 2; //大于5美元
            }
        } else if(bot.chain == macro.CHAIN.HECO){
            if(controller.totalRewardValue > 20) {
                coCount = 4; //大于13美元
            } else if(controller.totalRewardValue > 5) {
                coCount = 3; //大于5美元
            }
        }
        switch(bot.chain){
            case macro.CHAIN.MILKADA:
                let mixGas2 = mixGas.clone().sub();
                const opIndexs : number[] = [];
                for(let i = 0; i < coCount; i++){
                    opIndexs.push(opIndex);
                    opIndex = bot.getNextOperator(opIndex);
                }
                for(let i = 0; i < coCount; i++){
                    if(i == 0) Lazy.ins().log(`${macro.COLOR.BPurple}[pump] tx:${trans.hash} $${controller.totalRewardValue.toFixed(3)} ${macro.COLOR.BBlack}${mixGas.display()} gl:${controller.totalGas}${macro.COLOR.Off}`);

                    let delay = bot.handle.nextBlockTime() - 300; //延迟大约20ms
                    if(delay < 0) delay = 0;
                    delay = ~~((delay/(coCount-1)) * (coCount-i-1));
                    Lazy.ins().log1(` delay time: ${delay}`);
                    setTimeout(() => { this.batchPump(controller.data(), mixGas2, opIndexs[i], false, trans); }, delay);
                }
                break;
            case macro.CHAIN.PLS:

            default :
                for(let i = 0; i < coCount; i++){
                    if(i == 0) Lazy.ins().log(`${macro.COLOR.BPurple}[pump] tx:${trans.hash} $${controller.totalRewardValue.toFixed(3)} ${macro.COLOR.BBlack} p:${controller.data().length} ${mixGas.display()} gl:${controller.totalGas}${macro.COLOR.Off}`);
                    //console.log("controller.data().length 1: ", controller.data().length);
                    //15个一组
                    for(let j = 0; j < controller.data().length / 15; j++){
                        const p = controller.data().slice(j*15, j*15+15);
                        await this.batchPump(p, mixGas, opIndex, false, trans, isEoa);
                        opIndex = bot.getNextOperator(opIndex);
                        if(i==0 && j==0){
                            if(bot.chain == macro.CHAIN.PLS) {
                                const bEth = bot.token(bot.config.eth);
                                const cost  = bEth.toNum(mixGas.value().mul(BigNumber.from(500000))) * bEth.price;
                                if(cost < 0.01 && !isEoa){
                                    if(controller.totalRewardValue > 20){
                                        //价值大于10$, 增加一个吃套娃的高gas tx, 延迟执行
                                        let mixGas2 = mixGas.clone().litterMore();
                                        this.batchPump(p, mixGas2, opIndex, false, trans);
                                        opIndex = bot.getNextOperator(opIndex);
                                    }
                                    if(controller.totalRewardValue < 20 && controller.totalRewardValue > 0.1){
                                        //单次消耗小于1分钱增加一个尾tx，避免超跑
                                        if(bEth.toNum(mixGas.value().mul(BigNumber.from(500000))) * bEth.price < 0.01){
                                            let mixGas3 = mixGas.clone().sub();
                                            this.batchPump(p, mixGas3, opIndex, false, trans);
                                            opIndex = bot.getNextOperator(opIndex);
                                        }
                                    }
                                }
                            } else if(bot.chain == macro.CHAIN.ELA){
                                let mixGas2 = mixGas.clone().sub();
                                if(mixGas2.value().lt(ethers.utils.parseUnits('1', 'gwei'))){
                                    mixGas2 = mixGas.clone();
                                }
                                this.batchPump(p, mixGas2, opIndex, false, trans);
                                opIndex = bot.getNextOperator(opIndex);
                            } else if(bot.chain == macro.CHAIN.KAI){
                                //2美元以上的价值增加2个同级tx，一个高级tx
                                if(controller.totalRewardValue > 1){
                                    this.batchPump(p, mixGas, opIndex, false, trans);
                                    opIndex = bot.getNextOperator(opIndex);
                                    this.batchPump(p, mixGas, opIndex, false, trans);
                                    opIndex = bot.getNextOperator(opIndex);
                                    let higherGas = mixGas.clone().set(utils.parseUnits("33", 'gwei'));
                                    this.batchPump(p, higherGas, opIndex, false, trans);
                                    opIndex = bot.getNextOperator(opIndex);
                                }
                            }
                        }

                    }
                }

                break;
        }

        //TODO: layer2计算
        let controller2 = new FindPathController();
        //let results2 = new PumpData_old();
        for(let p of Object.values(pairCache)){
            const r0 = this.findPath(p.address, p.token0, mixGas, pairCache);
            const r1 = this.findPath(p.address, p.token1, mixGas, pairCache);
            controller2.add(r0);
            controller2.add(r1);
        }
        if(controller2.data().length > 0) {
            //console.log("controller.data().length 1: ", controller2.data().length);
            Lazy.ins().logTs(`${macro.COLOR.Purple}[pump2] tx:${trans.hash} $${controller2.totalRewardValue.toFixed(3)} ${macro.COLOR.Off}g:${mixGas.display()} gl:${controller.totalGas}`);
            //15个一组
            for(let j = 0; j < controller2.data().length / 15; j++){
                const p = controller2.data().slice(j*15, j*15+15);
                await this.batchPump(p, mixGas, opIndex, false, trans, isEoa);
                opIndex = bot.getNextOperator(opIndex);
            } 
        }

        //bot.client.safe.beginPumps(mixGas);

        //6个块后检查当前whalt的tx是否存在，判断假单
        this.checkTx(trans.hash, trans.from);
    }

    async mevLiquidity(pair:PairExtend, tokenA:string, tokenB:string, reserveA:number, reserveB:number, trans? : ethers.providers.TransactionResponse, bid=false){
        let controller = new FindPathController();
        /*
        let results : {[stable:string]: {
            rewards : number,
            paths: FindPathResult[]
        }} = {};
        */
        //缓存有变化的pair
        const pairCache : {[addr:string]:PairExtend} = {};
        const p = pair.clone();
        if(p.token0 == tokenA){
            p.reserve0 = reserveA;
            p.reserve1 = reserveB;
        } else {
            p.reserve1 = reserveA;
            p.reserve0 = reserveB;
        }
        pairCache[pair.address] = p;

        const mixGas = this.caclGas(trans);
        //正反计算一次
        let r = this.findPath(pair.address, tokenA, mixGas, pairCache);
        if(!r || r.length == 0) r = this.findPath(pair.address, tokenB, mixGas, pairCache);
        if(!r || r.length == 0) return;
        controller.add(r);

        let opIndex = bot.getOperatorIndex(trans?.from || macro.zeroAddress, bot.handle.blockNum);
            
        Lazy.ins().logTs(`${macro.COLOR.BPurple}[pump liq] tx:${trans?.hash || ""} $ ${controller.totalRewardValue.toFixed(3)}${macro.COLOR.Off} ${mixGas.display()}`);
        
        await this.batchPump(controller.data(), mixGas, opIndex, true, trans, pair.version == macro.PAIR_VERSION.V2_EOA);

        //10个一组
        //for(let i = 0; i < controller.data().length / 15; i++){
            //const p = controller.data().slice(i*15, i*15+15);
            //if(!bid){
            //    await this.batchPump(controller.data(), mixGas, opIndex, true, trans, pair.version == macro.PAIR_VERSION.V2_EOA);
            //} else {
            //    await bot.client.trash.batchTrash(controller.data(), mixGas, opIndex, true);
            //}
            //opIndex = bot.getNextOperator(opIndex, 1);
        //}

    }

    private async batchPump(
        paths:FindPathResult[],
        mixGas:MixGas,
        opIndex:number,
        isLiq? : boolean,
        trans? : ethers.providers.TransactionResponse,
        isEoa? : boolean,
    ){
        //console.log("[batchPump]: ", paths.length);
        const s = DataType.findPathResultsToServerPumpData(paths);

        if(isEoa && bot.config.eoaRouter.active){
            await bot.client.oneRouter.onMev(paths, mixGas);
            return;
        }
        let gasLimit = bot.chain == macro.CHAIN.MXC ? s.totalGas + 300000 : Math.max(macro.abi.botPumpGasLimit, s.totalGas + 300000); //mxc不够钱
        let signData = this.signTxSync(opIndex, s.encodePackedData, mixGas, gasLimit);

        //this._pump(signData, encodeData, stables[0], opIndex, totalRewardValue, isLiq, trans);
        if(bot.mode == macro.BOT_MODE.DEBUG || bot.mode == macro.BOT_MODE.LOCAL || bot.mode == macro.BOT_MODE.TEST) return;

        bot.handle.sendTransactionAll(signData, undefined,
            (receipt) => {
                bot.handle.nonce.update(receipt.from);

                //有tran的是pump
                this.log(receipt, s.data, s.totalRewardValue, opIndex, isLiq, trans);
                //console.log(`(${opIndex})[pump] finish: `, receipt.transactionHash);
                this.logChance(trans?.hash || "", s.totalRewardValue);
            },
            (pendingErrTx)=>{
                //console.log(pendingE);
                Lazy.ins().log1(`${macro.COLOR.Red}timeout or replaced tx: ${pendingErrTx}${macro.COLOR.Off}`);
            },
            (tx:string)=>{
                //console.log(`(${opIndex})[pump] fail`);
                //打包完成但是出错了 //pump
                Lazy.ins().log1(`${macro.COLOR.BRed}(${opIndex}) fail tx: ${tx}${macro.COLOR.Off}`);
                this.logFail(s, tx);
                this.logChance(trans?.hash || "", s.totalRewardValue);

            }
        );
    }

    private checkTx(hash:string, from:string, block = 6){
        const {COLOR} = macro; 
        setTimeout(() => {
            bot.provider().getTransactionReceipt(hash).then(tx => {
                //真单
                if(tx.status && tx.status == 1){
                    if(tx.blockNumber + 200 < bot.handle.blockNum){ //收到100个block之前的交易，是假单 (pluse)
                    let count = this.failPool.addAndCount(from);
                    //超过一次加入黑名单
                    if(!bot.blackList.has(from)){
                        bot.blackList.add(from);
                        Lazy.ins().log1(`${COLOR.BPurple}[checkTx][black list] old tx add: ${from}${COLOR.Off}`)
                    }
                    } else {
                        Lazy.ins().log1(`${COLOR.Green}[checkTx] true ${hash}, ${COLOR.BBlack}${from}${COLOR.Off}`);
                    } 
                } else {
                    Lazy.ins().log1(`${COLOR.Red}[checkTx] fail ${hash}, ${COLOR.BBlack}${from}${COLOR.Off}`);
                    //这是假单
                    let count = this.failPool.addAndCount(from);
                    //超过三次加入黑名单
                    if(count >= 3 * bot.config.securityLevel && !bot.blackList.has(from)){
                        bot.blackList.add(from);
                        Lazy.ins().log1(`${COLOR.BRed}[black list] many fail add: ${from}${COLOR.Off}`)
                    }
                }
            }).catch(e=>{
                Lazy.ins().log1(`${COLOR.BRed}[checkTx] fail ${hash}, ${COLOR.BBlack}${from}${COLOR.Off}`);
                //这是假单
                let count = this.failPool.addAndCount(from);
                //超过一次加入黑名单
                if(count >= 1 * bot.config.securityLevel && !bot.blackList.has(from)){
                    bot.blackList.add(from);
                    Lazy.ins().log1(`${COLOR.BRed}[checkTx][black list] bad tx add: ${from}${COLOR.Off}`)
                }

            });
        }, bot.config.blockTime * block * 1000);
    }

    private caclGas(trans? : ethers.providers.TransactionResponse){
        let mixGas = new MixGas(trans);
        /*
        if(bot.chain == macro.CHAIN.MXC){
            if(trans){
                let lowMixGas = new MixGas();
                if(mixGas.value().gt(lowMixGas.value())){
                    mixGas = lowMixGas;
                }
            }
        }
        */
        const pumpMax = utils.parseUnits(bot.config.pump.gasMax.toString(), 'gwei');

        if(mixGas.value().gt(pumpMax)){
            Lazy.ins().logTs1(` reach max gas: ${bot.config.pump.gasMax}`);
            if(bot.chain == macro.CHAIN.PLS || bot.chain == macro.CHAIN.MXC){
                //mixGas.maxFeePerGas
                mixGas.set(bot.handle.minGas());
            } else {
                mixGas.set(utils.parseUnits(bot.config.gasMin.toString(), 'gwei'));
            }
        }
        return mixGas;
    }

    findPath(pairAddr:string, tokenIntoPair:string, mixGas:MixGas, pairCache : {[addr:string]:PairExtend}, pathNum = bot.config.pump.maxPump || this.maxPumpDefault){
        let pair = bot.oracleV2.getPairByAddr(pairAddr);
        if(pair.mevPath.length == 0) return [];
        let result : FindPathResult[] = [];

        let rewards = 0;
        const reverse = tokenIntoPair == pair.token0; //mev生成的规则是左边token0, 右边token1
        for(let i = 0 ; i < Math.min(pair.mevPath.length, pathNum); i++){
            let mev = pair.mevPath[i];
            //console.log("[findPath]", mev);
            let newPairs = [...mev.pairs];
            let fees : number[];
            if(reverse){
                newPairs.reverse();
                fees = mev.fees1;
            } else {
                fees = mev.fees0;
            }
            let stable = reverse ? bot.token(mev.s1) : bot.token(mev.s0);
            const pairs_ref = newPairs.map((addr)=>{ return pairCache[addr] || bot.oracleV2.getPairByAddr(addr)});
            //console.log("[findPath]", bot.oracleV2.data.getTokenOuts(stable.address, pairs_ref.map(p=> p.address), true).join(" -> "));
            

            let amountNum = mev.type == macro.MEV_TYPE.BASE ? this.findMaxGolden(stable.address, pairs_ref, fees) : this.findMax(stable.address, pairs_ref, fees);
            //let amountNum = this.findMaxGolden(stable.address, pairs_ref, fees);
            if (amountNum * stable.price < this.amountInMin) continue;
            if(amountNum > 0){
                //console.log(`[findPath]  stable:${stable.symbol} pair:${pairs_ref.map(p=>p.address)} fees:${fees.join(",")} max:${amountNum}`);
                //console.log(pairs_ref);

                const gasCost =  mixGas.setGasLimit(BigNumber.from(mev.gas)).getGasCost(stable.address);
                const calc = this.getAmountsOutAndUpdateCache(
                    stable.address,
                    amountNum,
                    newPairs,
                    gasCost.num,
                    pairCache,
                    fees,
                    reverse ? mev.status1 == macro.MEV_STATUS.DISABLE : mev.status0 == macro.MEV_STATUS.DISABLE
                );
                if(calc.reward > 0){
                    result.push({
                        convertEth : mev.convertEth,
                        gas : mev.gas,
                        fees : fees,
                        mevType : mev.type,
                        tokenIn0or1 : reverse ? mev.tokenIn0or1[1] : mev.tokenIn0or1[0],

                        pairs : newPairs,
                        amount : stable.toBigNumber(Number(amountNum.toFixed(stable.decimals))),
                        reward : calc.reward,
                        rewardValue : calc.rewardValue,
                        
                        stable : stable.address,
                        gasCost : gasCost.bn,

                        maxFeePerGas: macro.bn.zero,
                        gasLimit : macro.bn.zero,

                        amounts: calc.amounts,
                        tokenOuts : calc.tokenOuts
                    });
                    rewards += calc.reward * stable.price;
                    
                    //加入到junk缓存
                    if(bot.config.junk.active && bot.config.trash.delay == 0){
                        bot.client.junk.addMevRecord(mev, calc.rewardValue);
                    }
                }
                //else if(mev.status !== macro.MEV_STATUS.DISABLE) {
                    //理想情况下，如果前面的pair没利润，后续的应该也是没有 (有其他bot搬平了)  //暂时关闭
                    //break;
                //}
            }
            
        }
        return result;
    }

    signTx(
        operatorIndex : number,
        data:string,
        sync:boolean,
        mixGas : MixGas,
        gasLimit = macro.abi.botPumpGasLimit,
        address = bot.config.bot.address){
        return new Promise<string>(async (resolve, reject)=>{
            const op = this.operators[operatorIndex].ins();
            const nonce = await bot.handle.nonce.use(op.address); //可能会阻塞, 自动更新nonce
            Lazy.ins().logTs1(`op: (${operatorIndex}) sign begin, nonce: ${nonce}, ${mixGas.display()}`);

            const tx : UnsignedTransaction = {
                to : address,
                gasLimit : gasLimit,
                data : data,
                nonce : nonce,
                chainId : bot.handle.block.chainId,
            };
            mixGas.attach(tx);
            if(sync){
                const signData = WasmSign.sign_full(tx, op.privateKey);
                resolve(signData);
            } else {
                const signData = await bot.signer.sign(tx, op.privateKey);
                resolve(signData);
            }
        });
    }

    signTxSync(
        operatorIndex : number,
        data:string,
        mixGas : MixGas,
        gasLimit = macro.abi.botPumpGasLimit,
        address = bot.config.bot.address,
        ovewriteNonce = -1
        ){
        
            const op = this.operators[operatorIndex].ins();
            const nonce = ovewriteNonce > -1 ? ovewriteNonce : bot.handle.nonce.useSync(op.address); //可能会阻塞, 自动更新nonce

            Lazy.ins().logTs1(`op: (${operatorIndex}) signSync begin, nonce: ${nonce}, ${mixGas.display()}`);

            const tx : UnsignedTransaction = {
                to : address,
                gasLimit : gasLimit,
                data : data,
                nonce : nonce,
                chainId : bot.handle.block.chainId,
            };
            mixGas.attach(tx);
            return WasmSign.sign_full(tx, op.privateKey);
    }

    async log(receipt:ethers.providers.TransactionReceipt, serverData:ServerPumpData[], expectRewards : number, seed:number, isLiq?:boolean, trans?:ethers.providers.TransactionResponse){
        if(!this.hashPool.enable(receipt.transactionHash)) return;

        //计算收益
        const { effectiveGasPrice, cumulativeGasUsed, gasUsed } = receipt;
        const bEth = bot.token(bot.config.eth);
        const costVal = bEth.toNum((effectiveGasPrice || cumulativeGasUsed).mul(gasUsed)) * bEth.price;

        const sum = {
            hash : receipt.transactionHash,
            block : receipt.blockNumber,
            index : receipt.transactionIndex,
            total : 0,
            success : 0,
            reward : 0
        };
        let results : number[] = [];
        let rewards : BigNumber[];

        const logs = (receipt as any)["logs"] as Array<any>;
        if(logs && logs.length > 0){
            let logData = logs[logs.length-1].data;
            [ results, rewards ]= bot.abiCoder.decode(["uint8[]","uint112[]"], logData);
            sum.total = results.length;
            for(let i = 0; i < serverData.length; i++){
                if(results[i] == macro.PUMP_RESULTS.Success || results[i] == macro.PUMP_RESULTS.SuccessOverlow){
                    sum.success++;
                    const pIn = bot.oracleV2.data.pm.get(serverData[i].pairs[0].addr);
                    const tokenIn = bot.token(serverData[i].tokenIn0or1.isZero() ? pIn.token0 : pIn.token1);
                    sum.reward += tokenIn.toNum(rewards[i]) * tokenIn.price;
                }
                
                // 如果检测到SwapErr，更新对应pair的mevPath
                if(results[i] == macro.PUMP_RESULTS.SwapErr) {
                    this.updatePairMevPath(serverData[i]);
                }
            }
        }

        let hasWarningResult = false;
        let rewardStr = "";
        //pump的交易记录
        //获取whale的交易顺序
        let whaleIndex = -1;
        let whaleBlockNum = -1;
        let whaleSuccess = false;

        if(trans){
            const whaleRes = await bot.provider().getTransactionReceipt(trans.hash);
            if(whaleRes){
                whaleIndex = whaleRes.transactionIndex;
                whaleBlockNum = whaleRes.blockNumber;
                whaleSuccess = whaleRes.status ? true : false;
            }
        }
        
        const blockGap = whaleBlockNum == -1 ? 0 : sum.block - whaleBlockNum;

        if(sum.reward > 0){
            results.forEach(_r=>{
                switch(_r){
                    case macro.PUMP_RESULTS.None:
                        hasWarningResult = true;
                        break;
                    case macro.PUMP_RESULTS.Balance0:
                        hasWarningResult = true;
                        //没有钱
                        break;
                    case macro.PUMP_RESULTS.SuccessOverlow:
                        hasWarningResult = true;
                        //overflow
                        break;
                    case macro.PUMP_RESULTS.SwapErr:
                        hasWarningResult = true;
                        break;
                    case macro.PUMP_RESULTS.FindMaxErr:
                        hasWarningResult = true;
                        break;
                    default:
                        break;
                }
            });
            rewardStr = `$ ${sum.reward.toFixed(3)} ${macro.COLOR.BBlack}- ${costVal.toFixed(3)} = ${(sum.reward-costVal).toFixed(3)} (${expectRewards.toFixed(3)}) [${sum.success}/${sum.total}] ${(isLiq && !trans) ? "*": ""}`;
            let logData = [
                `$ ${sum.reward.toFixed(3)} - ${costVal.toFixed(3)} = ${(sum.reward-costVal).toFixed(3)} (${expectRewards.toFixed(3)})`,
                `${sum.success}/${sum.total}`,
                `${blockGap==0 ? "" : "(+" +blockGap+")"}${sum.index}/${whaleIndex}${whaleSuccess?"":"x"}`
            ];
            if(hasWarningResult) logData.push(results.join(","));
            bot.localLog.append(isLiq ? macro.FILE.LOG_PUMP_LIQ : macro.FILE.LOG_PUMP, sum.hash, logData);

        } else {
            rewardStr = `${macro.COLOR.BBlack}$ -${costVal.toFixed(3)} (${expectRewards.toFixed(3)})`;
        }

        if(sum.reward == 0 && blockGap == 0 && sum.index == whaleIndex+1){
            //跟上swap但是却没有收益，可能计算误差或者是pair没更新，手动更新一次
            let updateTotalPair = 0;
            serverData.forEach(_s=>{
                _s.pairs.forEach(_p=>{
                    updateTotalPair++;
                    bot.oracleV2.data.pm.get(_p.addr).getReservesServer(true);
                });
            });
            Lazy.ins().log1(` ${macro.COLOR.Yellow}*** update ${updateTotalPair} pairs.${macro.COLOR.Off}`);
        }
        
        if(whaleBlockNum > 1 && blockGap > 3600 / bot.config.blockTime) { //收到了1个小时前的pending
            //pls可能会收到几天前的pending，这种情况重启一下就可以解决
            bot.localLog.append(macro.FILE.LOG_PUMP_BAD_PENDING, sum.hash , [`${whaleBlockNum}`, `${sum.block}`]);

        }
        Lazy.ins().log(`${macro.COLOR.Green} op:(${seed}) ${isLiq ? "pump liq" : "pump"} ${blockGap == 0 ? "   " : "("+blockGap+")"}${whaleBlockNum} `
                    +`${macro.COLOR.BGreen}(${sum.index}/${whaleIndex}${whaleSuccess?"":"x"}) `
                    +`${rewardStr} `
                    +`${hasWarningResult ? macro.COLOR.Yellow : macro.COLOR.BBlack}${results.join(",")}${macro.COLOR.Off}`);
        
        
    }

    // 更新pair的mevPath
    private async updatePairMevPath(serverPumpData: ServerPumpData) {
        try {
            const pairsToUpdate = new Set<string>();
            
            // 收集所有需要更新的pairs
            for(const pairData of serverPumpData.pairs) {
                pairsToUpdate.add(pairData.addr);
            }
            
            Lazy.ins().logTs(`${macro.COLOR.Yellow}[updatePairMevPath] 开始更新 ${pairsToUpdate.size} 个 pairs 的 mevPath${macro.COLOR.Off}`);
            
            for(const pairAddr of pairsToUpdate) {
                const pair = bot.oracleV2.data.pm.get(pairAddr);
                if(!pair) continue;
                
                // 获取该pair的mevPath
                const mevPaths = bot.oracleV2.data.getMevPath(pairAddr);
                if(mevPaths.length === 0) continue;
                
                // 使用batchCheckMev检查并更新mevPath
                Lazy.ins().log(`${macro.COLOR.Yellow}检查并更新pair: ${pairAddr} (${pair.token0Symbol}-${pair.token1Symbol}) 的 mevPath${macro.COLOR.Off}`);
                
                const res = await bot.contractIns.batchCheckMev(mevPaths);
                
                for(let i = 0; i < mevPaths.length; i++) {
                    if(pair.version == macro.PAIR_VERSION.V2_EOA) {
                        const length = mevPaths[i].pairs.length;
                        mevPaths[i].fees0 = new Array(length).fill(0);
                        mevPaths[i].fees1 = new Array(length).fill(0);
                        mevPaths[i].status0 = macro.MEV_STATUS.ACTIVE;
                        mevPaths[i].status1 = macro.MEV_STATUS.ACTIVE;
                        mevPaths[i].gas = 500000;
                    } else {
                        mevPaths[i].fees0 = res[i].fee0;
                        mevPaths[i].fees1 = res[i].fee1;
                        mevPaths[i].gas = res[i].gas;
                        mevPaths[i].status0 = mevPaths[i].fees0.length > 0 ? macro.MEV_STATUS.ACTIVE : macro.MEV_STATUS.DISABLE;
                        mevPaths[i].status1 = mevPaths[i].fees1.length > 0 ? macro.MEV_STATUS.ACTIVE : macro.MEV_STATUS.DISABLE;
                    }
                    Lazy.ins().log(`${macro.COLOR.Blue}fee0: ${mevPaths[i].fees0.join(", ")}, fee1: ${mevPaths[i].fees1.join(", ")}${macro.COLOR.Off}`);
                }
                // 更新pair的mevPath
                pair.mevPath = mevPaths;
            }
            
            Lazy.ins().log(`${macro.COLOR.Green}[updatePairMevPath] 完成更新${macro.COLOR.Off}`);
        } catch(e) {
            Lazy.ins().log(`${macro.COLOR.Red}[updatePairMevPath] 更新失败: ${e}${macro.COLOR.Off}`);
        }
    }

    private async logChance(hash:string, rewardsValue:number){
        bot.localLog.append(macro.FILE.LOG_PUMP_WHALE, hash, [`${rewardsValue.toFixed(5)}`]);
    }

    async logFail(s : FindPathResultsToServerPumpData, hash:string){
        Lazy.ins().log1(`${macro.COLOR.BIRed}[FailTx] debuging fail tx: ${hash}${macro.COLOR.Off}`);

        let req = await bot.provider().getTransaction(hash);
        if(!req){
            Lazy.ins().log1(`${macro.COLOR.Red}[FailTx] fail to get tx ${macro.COLOR.Off}`);
            return;
        }
        const gasLimit = req.gasLimit.toNumber();

        let res = await bot.provider().getTransactionReceipt(hash);
        const gasUsed = res.gasUsed.toNumber();
        const percent = gasUsed * 100/gasLimit;
        Lazy.ins().log1(`${macro.COLOR.BIRed}[FailTx] gasLimit/gasUsed: ${gasLimit}/${gasUsed}, ${percent.toFixed(3)}%${macro.COLOR.Off}`);
        //gas不够
        if(percent > 93){
            bot.localLog.append(macro.FILE.LOG_PUMP_FAIL, hash, ["OUT_OF_GAS"]);
        } else {
            bot.localLog.append(macro.FILE.LOG_PUMP_FAIL, hash, ["UNKNOW"]);
            s.data.forEach(_s=>{
                const p0 = bot.oracleV2.data.pm.get(_s.pairs[0].addr);
                const tokenIn = _s.tokenIn0or1.isZero() ? p0.token0 : p0.token1;
                Lazy.ins().log1(`${macro.COLOR.BRed}ERR: ${bot.oracleV2.data.getTokenOuts(tokenIn, _s.pairs.map(_p=>_p.addr), true).join(" -> ")}${macro.COLOR.Off}`);
                Lazy.ins().log1(`${macro.COLOR.BBlack}${_s.pairs.map(_p=>_p.addr).join(", ")}${macro.COLOR.Off}`);
                Lazy.ins().log1(`tokenIn0or1: ${_s.tokenIn0or1.toNumber()}`);
            });
        }
        //BAD_PAIR
        return;
    }

    getAmountsOutAndUpdateCache(tokenIn:string, amountIn:number, pairAddrs:string[], gasCost=0, cache : {[addr:string]:PairExtend}= {}, fees:number[], isBad = false){
        const tokenOuts:string[] = [];
        const amounts = [amountIn];
        const isBlackList = isBad;
        let isSamedecimals = true;
        tokenOuts[0] = tokenIn;
    
        for(let i = 0 ; i < pairAddrs.length; i++){
            tokenIn = tokenOuts[i];
            const {token0, token1, reserve0, reserve1, version, fp} = cache[pairAddrs[i]] || bot.oracleV2.getPairByAddr(pairAddrs[i]);
            tokenOuts[i+1] = tokenIn == token0 ? token1 : token0;
            const [reserveIn, reserveOut] = tokenIn == token0 ? [reserve0, reserve1] : [reserve1, reserve0];
            //amounts[i+1] = tools.getAmountOut(amounts[i], reserveIn, reserveOut);
            amounts[i+1] = tools.getAmountOut(amounts[i], reserveIn, reserveOut, (fp - fees[i])/100000, version == macro.PAIR_VERSION.V2_STABLE);
        }
        let reward = amounts[amounts.length-1] - amounts[0] - gasCost;
        
        const bIn = bot.token(tokenOuts[0]);
        let rewardUsd = bIn.price * reward;

        if(rewardUsd > this.rewardMinUsd){
            let _token = "";
            for(let z = 0 ; z < amounts.length; z++){
                _token += `${bot.token(tokenOuts[z]).symbol}(${amounts[z].toFixed(3)}) ${z==amounts.length-1 ? "" : "-> "}`;
            }    
            const bOut = bot.token(tokenOuts[tokenOuts.length-1]);
            if(bIn.address !== bOut.address){
                _token += "*";
                let sBalance = bot.client.stableBalance.get(bIn.address);
                let config = bot.config.tokens[bIn.address];
                //超过的币种的keep，不进行这笔跨币种的交易
                if(config.keep && config.keep > sBalance && bot.mode != macro.BOT_MODE.LOCAL){
                    _token += `${macro.COLOR.BRed}${config.name} < ${config.keep} notEnough${macro.COLOR.Off}`;
                    reward = 0;
                } else if(bIn.decimals !== bOut.decimals){
                    //不同精度的币种交易，走swap接口
                    isSamedecimals = false;
                }
            }
            if(isBlackList){
                Lazy.ins().log1(`${macro.COLOR.Red}${_token} $${reward.toFixed(4)}/${gasCost.toFixed(4)} [bad pump]${macro.COLOR.Off}`);
                //未通过，尝试使用swap，加入safe缓存
                if(isBad) bot.client.safe.tryPump(bIn.address, bOut.address, amountIn, amountIn, pairAddrs);
            } else {
                Lazy.ins().log1(`${macro.COLOR.Green}${_token} +${reward.toFixed(4)} ${macro.COLOR.BBlack}g:${gasCost.toFixed(4)} $${rewardUsd.toFixed(3)}${macro.COLOR.Off}`);
            }
        }

        //if(bot.chain == macro.CHAIN.KLAY && tokenOuts.map(t=>bot.token(t).symbol).join(",").includes("DFK")) reward = 0;
        if(rewardUsd < this.rewardMinUsd || isBlackList) reward = 0;
        
        if(rewardUsd > this.rewardMinUsd){
            for(let i = 0 ; i < pairAddrs.length; i++){
                cache[pairAddrs[i]] ??= bot.oracleV2.getPairByAddr(pairAddrs[i]).clone();
                cache[pairAddrs[i]].updateBalance(tokenOuts[i], amounts[i], amounts[i+1]);
            }
        }

        return {
            amounts : amounts, //amount1 -> amount2 -> amount3
            tokenOuts : tokenOuts, //token1 -> token2 -> token3
            reward : reward,
            rewardValue : rewardUsd,
            sameDecimals : isSamedecimals
        }
    }

    //针对所有pair都是v2
    findMax(tokenIn:string, pairs:PairExtend[], fees:number[]){
        //console.time("findMaxTime");
        //console.log("findMax: ", tokenIn, fees);
        let Ea = 0, Eb = 0, ra = 0, rb = 0, rb1 = 0, rc = 0, Efee = 0, fee = 0, tokenOut = "";
        //length = 1
        const p0 = pairs[0];
        if(pairs.length == 1){ //服务端也要适配
            if(p0.token0 == tokenIn) {
                Ea = p0.reserve0;;
                Eb = p0.reserve1;
                tokenOut = p0.token1;
            } else {
                Ea = p0.reserve1;;
                Eb = p0.reserve0;
                tokenOut = p0.token0;
            }
            Efee = (p0.fp - fees[0]) / 100000;
            //Efee = p0.fee0;
            return (Math.sqrt(Ea * Eb * Efee) - Ea) / Efee;
        }
        //length >= 2
        const p1 = pairs[1];
        
        if(p0.token0 == tokenIn) {
            ra = p0.reserve0;
            rb = p0.reserve1;
            tokenOut = p0.token1;
        } else {
            ra = p0.reserve1;
            rb = p0.reserve0;
            tokenOut = p0.token0;
        }
        Efee = (p0.fp - fees[0]) / 100000;
        //Efee = p0.fee0;
        //console.log(Efee);
    
        if(p1.token0 == tokenOut){
            rb1 = p1.reserve0;
            rc  = p1.reserve1;
            tokenOut = p1.token1;
        } else {
            rb1 = p1.reserve1;
            rc  = p1.reserve0;
            tokenOut = p1.token0;
        }
        fee = (p1.fp - fees[1]) / 100000;
        //fee = p1.fee0;
        //console.log(fee);
    
        Ea = ra * rb1 / (rb1 + rb * fee);
        Eb = fee * rb * rc / (rb1 + rb * fee);
    
        for(let i = 2 ; i < pairs.length; i++){
            ra = Ea;
            rb = Eb;
            const pn = pairs[i];
            if(pn.token0 == tokenOut){
                rb1 = pn.reserve0;
                rc  = pn.reserve1;
                tokenOut = pn.token1;
            } else {
                rb1 = pn.reserve1;
                rc  = pn.reserve0;
                tokenOut = pn.token0;
            }
            fee = (pn.fp - fees[i]) / 100000;
            //fee = pn.fee0;
            Ea = ra * rb1 / (rb1 + rb * fee);
            Eb = fee * rb * rc / (rb1 + rb * fee);
        }
    
        //return [Ea, Eb, Efee];
        //console.timeEnd("findMaxTime");
        return (Math.sqrt(Ea * Eb * Efee) - Ea) / Efee;
    }
 
    private getAmountOutByPairEx(tokenIn:string, amountIn:number, pairs:PairExtend[], fees:number[]){
        if(amountIn == 0) return 0;
        let tokenOut = tokenIn;
        let amountOut = amountIn;
        for(let i = 0 ; i < pairs.length; i++){
            if(i > 0){
                tokenIn = tokenOut;
                amountIn = amountOut;
            }
            const {token0, token1, reserve0, reserve1, version, fp} = pairs[i];
            tokenOut = tokenIn == token0 ? token1 : token0;
            const [reserveIn, reserveOut] = tokenIn == token0 ? [reserve0, reserve1] : [reserve1, reserve0];
            //amountOut = tools.getAmountOut(amountIn, reserveIn, reserveOut, tokenIn == token0 ? fee0 : fee1, version == macro.PAIR_VERSION.V2_STABLE);
            amountOut = tools.getAmountOut(amountIn, reserveIn, reserveOut, (fp - fees[i])/100000, version == macro.PAIR_VERSION.V2_STABLE);
        }
        return amountOut;
    }

    //针对存在非v2 pair
    findMaxGolden(tokenIn:string, pairs:PairExtend[], fees:number[]){
        //console.time("GoldenSection");
        let low = 0.000001 / bot.token(tokenIn).price; //最低0.005usd的币
        const tolerance = low * 10000; //精度$0.01
        let high = bot.client.stableBalance.get(tokenIn) || 100000000; //持仓总额;
        //console.log(`low:${low}, tolerance:${high}`);
        //let high = 100000;
        
        //先计算最低档是否有利润
        let amountOut1 = this.getAmountOutByPairEx(tokenIn, low, pairs, fees);
        if(amountOut1 < low) return 0;

        let step = 0;
        const phi = 1.618034;
        
        let x1 = high - ((high - low) / phi);
        let x2 = low  + ((high - low) / phi);
        
        amountOut1 = this.getAmountOutByPairEx(tokenIn, x1, pairs, fees);
        let amountOut2 = this.getAmountOutByPairEx(tokenIn, x2, pairs, fees);
        
        let f1 = amountOut1 - x1;
        let f2 = amountOut2 - x2;
        
        while(high > tolerance + low && step < 40){
            if(f1 > f2){
                high = x2;
                x2 = x1;
                f2 = f1;
                x1 = high - ((high - low) / phi);
                amountOut1 = this.getAmountOutByPairEx(tokenIn, x1, pairs, fees);
                f1 = amountOut1 - x1;
            } else if (f1 < f2){
                low = x1;
                x1 = x2;
                f1 = f2;
                x2 = low  + ((high - low) / phi);
                amountOut2 = this.getAmountOutByPairEx(tokenIn, x2, pairs, fees);
                f2 = amountOut2 - x2;
            } else {
                low = x1;
                high = x2;
                x1 = high - ((high - low) / phi);
                x2 = low  + ((high - low) / phi);
                amountOut1 = this.getAmountOutByPairEx(tokenIn, x1, pairs, fees);
                amountOut2 = this.getAmountOutByPairEx(tokenIn, x2, pairs, fees);
                f1 = amountOut1 - x1;
                f2 = amountOut2 - x2;
            }
            step++;
            //console.log(`[${step.toString().padStart(3, " ")}] fx1:${f1} fx2:${f2} lower:${low} upper:${high}`);
        }
        //console.log("GoldenSection step:",step);
        //console.timeEnd("GoldenSection");
        return (high + low) / 2;
    }
}