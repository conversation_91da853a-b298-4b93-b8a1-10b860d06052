use std::{error::Error, sync::{LazyLock, OnceLock}};
use futures::StreamExt;

use crate::{config::ChainConfig, connector::{ChainStatus, Connector}, user::User, vira::{status::sync, Vira}};

mod master;
mod config;
mod tools;
mod vira;
mod user;
mod errors;
mod connector;
mod strategy;
mod test;
mod executor;

use tracing_subscriber;

// chain config
pub static CONFIG: LazyLock<ChainConfig> = LazyLock::new(|| {
    crate::config::x::new()
    //crate::config::pls::new()
});
// chain status
static STATUS: LazyLock<ChainStatus> = LazyLock::new(|| ChainStatus::new());
// use status
static USER : LazyLock<User> = LazyLock::new(|| User::new());

static VIRA : OnceLock<Vira> = OnceLock::new();

#[tokio::main]
async fn main() -> Result<(), Box<dyn Error>> {
    init().await?;
    Ok(())
}

async fn init() -> Result<(), Box<dyn Error>> {


    // 初始化 tracing 日志
    //tracing_subscriber::fmt().with_env_filter("alloy_transport=trace,alloy_provider=trace").init();

    let mut vira = Vira::new().await;
    //初始化bot余额
    vira.contract.update_balance().await?;

    // 调用init方法
    vira.sm.init(&vira.contract, &vira.connector).await?;
    sync::check_all_pools_mevs(&vira.contract, &vira.sm.pools).await?;
    vira.sm.save()?;

    let _ = VIRA.set(vira);
    let vira = VIRA.get().unwrap();
    //更新nonce
    USER.nonce.update_all().await;

    // 创建一个通道用于通知trash组件同步完成
    let (sync_complete_tx, sync_complete_rx) = tokio::sync::mpsc::channel(1);
    
    tokio::spawn(async move {
        match sync::sync_all_reserves(&vira.sm.pools, &vira.connector).await {
            Ok(()) => {
                println!("同步储备金完成");
                // 通知trash组件同步已完成
                let _ = sync_complete_tx.send(()).await;
                //auto drop sync_complete_tx
            }
            Err(e) => {
                eprintln!("同步储备金失败: {:?}", e);
            }
        }
    });

    let block_stream = Connector::subscribe_block().await.expect("订阅block失败");
    let logs_stream = Connector::subscribe_logs().await.expect("订阅logs失败");
    
    let mut strategy = strategy::Strategy::new();

    // 使用 tokio::spawn 创建并发任务来监听 block_stream 和 logs_stream
    // 这样可以确保两个监听任务并发执行，不会相互阻塞
    let trash_handle = tokio::spawn(async move {
        if let Err(e) = strategy.trash.listen(logs_stream, sync_complete_rx).await {
            eprintln!("Trash listener error: {:?}", e);
        }
    });

    let block_handle = tokio::spawn(async move {
        futures::pin_mut!(block_stream);
        loop {
            match block_stream.next().await {
                Some(_block) => {
                    //println!("block: {}", block.number);
                }
                None => {
                    println!("Block stream ended");
                    break;
                }
            }
        }
    });

    // 使用 tokio::select! 宏来同时监听两个任务
    // 如果任一任务完成或出错，都会被适当处理
    tokio::select! {
        result = trash_handle => {
            if let Err(e) = result {
                eprintln!("Trash task panicked: {:?}", e);
            }
        }
        result = block_handle => {
            if let Err(e) = result {
                eprintln!("Block task panicked: {:?}", e);
            }
        }
    }
    println!("One of the listening tasks has completed");

    Ok(())
}


#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_main() -> Result<(), Box<dyn Error>> {
        init().await?;
        Ok(())
    }
}