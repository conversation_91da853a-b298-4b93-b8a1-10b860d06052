pub mod error;
pub mod nonce;

use std::{pin::Pin, sync::{atomic::{AtomicU64, Ordering}, OnceLock}};

use alloy::{consensus::Transaction as ConsensusTransaction, eips::{BlockId, BlockNumberOrTag}, network::{Ethereum, EthereumWallet, Network}, primitives::Address, providers::{fillers::{FillProvider, JoinFill, WalletFiller}, Identity, Provider, ProviderBuilder, RootProvider}, rpc::types::{Filter, Transaction}, signers::local::PrivateKeySigner};
use alloy_transport_ws::{WsConnect, WebSocketConfig};
use colored::Colorize;
use futures::Stream;
use tokio::sync::mpsc;
use tokio_stream::{wrappers::ReceiverStream, StreamExt};

use crate::{connector::nonce::NonceManager, tools::now_str, vira::{consts::GET_LOGS_TIMEOUT, errors::DEXError, pool::DexPool}, CONFIG, STATUS, VIRA};

pub type ViraProvider = FillProvider<JoinFill<Identity, WalletFiller<EthereumWallet>>, RootProvider>;

#[derive(Debug, Clone)]
 pub struct Connector
 {
    pub url : String,
    //pub provider : Arc<RootProvider<Ethereum>>,

    pub provider : ViraProvider,
 }
 
 
impl Connector
{
    pub async fn new(str:&str) -> Self {
        let signer = PrivateKeySigner::random();
        let mut wallets = EthereumWallet::new(signer);
        //把config的所有operator私钥加入到wallets
        for operators in CONFIG.operators.iter() {
            for (_, key) in operators.iter() {
                let signer :PrivateKeySigner = key.parse().expect("PrivateKeySigner parse error");
                wallets.register_signer(signer);
            }
        }

        let provider = if str.starts_with("ws://") || str.starts_with("wss://") {
            // WebSocket连接，配置自动重连参数
            println!("{}", format!("WebSocket连接: {}", str).green().bold());
            
            // 配置WebSocket连接参数
            let mut ws_config = WebSocketConfig::default();
            ws_config.max_frame_size = Some(10 * 1024 * 1024); // 10MB
            ws_config.max_message_size = Some(10 * 1024 * 1024); // 10MB
            
            // 创建WebSocket连接配置
            let ws_connect = WsConnect::new(str)
                .with_config(ws_config)
                .with_max_retries(10)  // 最大重试次数
                .with_retry_interval(std::time::Duration::from_secs(5)); // 重试间隔3秒

            // 使用connect_ws方法创建WebSocket连接
            ProviderBuilder::<_, _, Ethereum>::default()
                .wallet(wallets)
                .connect_ws(ws_connect)
                .await
                .expect("WebSocket provider init err")
        } else {
            // HTTP连接
            println!("{}", format!("HTTP连接: {}", str).green().bold());
            
            ProviderBuilder::<_, _, Ethereum>::default()
                .wallet(wallets)
                .connect_http(str.parse().expect("Invalid URL"))
        };

        let c = Connector {url : str.to_string(), provider : provider,};
        c.init().await;
        c
    }

    pub fn provider(&self) -> &ViraProvider {
        &self.provider
    }

    async fn init(&self){
        let chain_id = self.provider.get_chain_id().await.expect("get chain_id error");
        STATUS.chain_id.set(chain_id).expect("chain_id set error");
        println!("[connector init] chain_id: {}", chain_id);
        let block = self.provider.get_block(BlockId::latest()).await.expect("get block error").expect("get block error 2");

        let base_fee_per_gas = block.header.base_fee_per_gas;
        STATUS.is_eip1559.set(base_fee_per_gas.is_some()).expect("set base_fee_per_gas error");
        println!("[connector init] supports_eip1559: {}", base_fee_per_gas.is_some());

        if let Some(fee) = base_fee_per_gas {
            println!("base_fee_per_gas : {}", fee);
        } else {
            //如果get_gas_price成功，则设置STATUS.support_get_gas_price为true
            let gas_price = self.provider.get_gas_price().await;
            STATUS.support_get_gas_price.set(gas_price.is_ok()).expect("set gas_price error");

            match gas_price {
                Ok(gas_price) => {
                    STATUS.update_gas_price(gas_price);
                    println!("gas_price : {}", gas_price);
                },
                Err(e) => {
                    println!("get_gas_price error: {}", e);
                    if let Some(gas) = CONFIG.fix_gas {
                        STATUS.update_gas_price_from_cfg(gas);
                    } else {
                        panic!()
                    }
                }
                
            }
        }
    }

    pub async fn subscribe_block() -> Result<Pin<Box<dyn Stream<Item = <Ethereum as Network>::HeaderResponse> + Send>>, DEXError> {
        // 创建通道用于发送区块头信息
        let (block_tx, block_rx) = mpsc::channel(16);

        // 克隆所需数据
        let provider = &VIRA.get().unwrap().connector.provider;

        tokio::spawn(async move {
            // 订阅区块头
            let mut block_stream = match provider.subscribe_blocks().await {
                Ok(stream) => stream.into_stream(),
                Err(e) => {
                    println!("Block subscription failed: {e}");
                    return;
                }
            };

            loop {
                tokio::select! {
                    // 处理新区块头
                    Some(block) = block_stream.next() => {
                        //每60秒更新一次
                        if !STATUS.is_eip1559() &&  STATUS.support_get_gas_price() && block.number % (600 /CONFIG.block_time) == 0  {
                            let gas_price = provider.get_gas_price().await;
                            match gas_price {
                                Ok(gas_price) => {
                                    crate::STATUS.update_gas_price(gas_price);
                                },
                                Err(e) => {
                                    println!("get_gas_price error: {}", e);
                                }
                            }
                        } else {
                            crate::STATUS.update_base_fee(block.base_fee_per_gas);
                        }
                        STATUS.update_latest_block(block.number);
                        // 尝试发送区块头信息
                        if let Err(e) = block_tx.send(block).await {
                            println!("Failed to send block header: {e}");
                            break;
                        }
                    }
                    // 如果block_stream已关闭，则退出循环
                    else => {
                        println!("{}", "subscribe_block stream closed, stopping...".to_string().red());
                        break;
                    }
                }
            }
        });

        // 创建并返回区块头流
        let block_stream = Box::pin(ReceiverStream::new(block_rx));
        Ok(block_stream)
    }

    pub async fn subscribe_block_and_logs(&self) -> Result<Pin<Box<dyn Stream<Item = Vec<Address>> + Send>>, DEXError> 
    {
        // 创建专用通道 - 分离两种事件
        let (amm_tx, amm_rx) = mpsc::channel(16);
        let provider = &VIRA.get().unwrap().connector.provider;

        tokio::spawn(async move {
            use futures::stream::FuturesUnordered;
            
            let mut block_stream = match provider.subscribe_blocks().await {
                Ok(stream) => stream.into_stream(),
                Err(e) => {
                    println!("Block subscription failed: {e}");
                    return;
                }
            };
            
            let mut last_processed_block = 0u64;
            // 创建一个FuturesUnordered来管理并发的get_logs请求
            let mut log_futures = FuturesUnordered::new();
            
            use tokio::time::timeout;
            let vira = &VIRA.get().unwrap();
            let pools = &vira.sm.pools;
            let provider = &vira.connector.provider;

            loop {
                tokio::select! {
                    // 处理新区块
                    Some(block) = block_stream.next() => {
                        let end_block = block.number;
                        //block.inner.base_fee_per_gas = block.base_fee_per_gas;
                        let current = last_processed_block;
                        // 跳过旧区块
                        if end_block <= current { continue; }
                        
                        // start_block等于last_processed_block，并且不小于end_block-20
                        let min_block = end_block.saturating_sub(20);
                        let start_block = last_processed_block.max(min_block);
                        last_processed_block = end_block;
                        
                        // 创建日志获取任务并添加到FuturesUnordered
                        let filter = Filter::new().from_block(start_block).to_block(end_block);
                        let amm_tx_clone = amm_tx.clone();
                        
                        // 在任务中返回是否成功处理区块的标志，用于错误处理
                        log_futures.push(async move {
                            // 定义一个错误处理函数，减少重复代码
                            let handle_error = |error_msg: String| {
                                println!("{}", error_msg.red().bold());
                                // 更新最新区块
                                crate::STATUS.update_base_fee(block.base_fee_per_gas);
                                crate::STATUS.update_latest_block(end_block);
                                (end_block, false) // 返回区块号和失败标志
                            };
                            
                            // 获取日志
                            let logs = match timeout(GET_LOGS_TIMEOUT, provider.get_logs(&filter)).await {
                                Ok(Ok(logs)) => logs,
                                Ok(Err(e)) => {
                                    return handle_error(format!("[ERR] Get logs failed for blocks {start_block}-{end_block}: {e}"));
                                }
                                Err(_) => {
                                    return handle_error(format!("[ERR] Get logs timed out for blocks {start_block}-{end_block}"));
                                }
                            };

                            // 同步池状态
                            let affected_amms = match pools.sync(&logs) {
                                Ok(amms) => amms,
                                Err(e) => {
                                    return handle_error(format!("[ERR] Pool sync failed: {e}"));
                                }
                            };
                            if affected_amms.len() > 0 {
                                println!("{}", format!("{} block: {start_block} ~ {end_block}, logs: {} / {}", now_str(), affected_amms.len(), logs.len()).bright_black());
                            }
                            
                            // 更新最新区块
                            crate::STATUS.update_base_fee(block.base_fee_per_gas);
                            crate::STATUS.update_latest_block(end_block);

                            // 发送AMM更新
                            if !affected_amms.is_empty() {
                                let _ = amm_tx_clone.send(affected_amms).await;
                            }
                            
                            (end_block, true) // 返回区块号和成功标志
                        });
                    }
                    
                    // 处理完成的日志获取任务
                    Some((processed_block, success)) = log_futures.next(), if !log_futures.is_empty() => {
                        // 如果处理失败，记录日志但不做额外操作
                        if !success {
                            println!("{}", format!("区块 {} 处理失败，但已更新最新区块号以避免卡住", processed_block).yellow());
                        }
                    }
                    
                    // 如果没有正在进行的任务且block_stream已关闭，则退出循环
                    else => {
                        if log_futures.is_empty() {
                            println!("{}", "subscribe_block subscribe_block_and_logs closed, stopping...".to_string().red());
                            break;
                        }
                    }
                }
            }
        });
    
        // 创建专用流
        let amm_stream = Box::pin(ReceiverStream::new(amm_rx));
    
        Ok(amm_stream)
    }

    pub async fn subscribe_pending() -> Result<Pin<Box<dyn Stream<Item = Transaction> + Send>>, DEXError> 
    {
        let (receipt_tx, receipt_rx) = mpsc::channel(64); // 收据可能需要更大缓冲区

        // 克隆所需数据
        let provider = &VIRA.get().unwrap().connector.provider;

        tokio::spawn(async move {
            use futures::stream::FuturesUnordered;
            use std::collections::VecDeque;
            
            let mut pending_stream = match provider.subscribe_pending_transactions().await {
                Ok(stream) => stream.into_stream(),
                Err(e) => {
                    println!("Pending transaction subscription failed: {e}");
                    return;
                }
            };
            
            // 创建一个FuturesUnordered来管理并发的交易查询
            let mut tx_futures = FuturesUnordered::new();
            
            // 创建一个固定大小的队列来存储最近处理过的交易哈希
            const MAX_RECENT: usize = 24;
            const CLEANUP_THRESHOLD: usize = MAX_RECENT + 12;
            let mut recent_tx_hashes = VecDeque::with_capacity(CLEANUP_THRESHOLD);
            
            loop {
                tokio::select! {
                    // 处理新的pending交易
                    Some(tx_hash) = pending_stream.next() => {
                        // 检查是否已经处理过这个交易哈希
                        if recent_tx_hashes.contains(&tx_hash) {
                            // 已经处理过，跳过
                            // println!("{} {} [已处理], buff({})", now_str(), tx_hash.to_string().yellow().bold(), receipt_tx.capacity());
                            continue;
                        }
                        
                        //println!("{} {}", now_str(), tx_hash.to_string().blue());
                        
                        // 添加到最近处理过的交易哈希列表
                        recent_tx_hashes.push_back(tx_hash);
                        // 如果列表超过最大容量，移除最旧的元素
                        if recent_tx_hashes.len() > CLEANUP_THRESHOLD {
                            let remove_count = recent_tx_hashes.len() - MAX_RECENT;
                            recent_tx_hashes.drain(0..remove_count);
                        }
                        
                        // 创建交易查询任务并添加到FuturesUnordered
                        let provider_clone = provider.clone();
                        let receipt_tx_clone = receipt_tx.clone();
                        
                        tx_futures.push(async move {
                            // 尝试获取交易
                            if let Ok(Some(tx)) = provider_clone.get_transaction_by_hash(tx_hash).await {
                                print!("\r{} {:?} -> {:?}", now_str(), tx.inner.signer(), tx.inner.to());
                                let _ = receipt_tx_clone.send(tx).await;
                            }
                        });
                    }
                    
                    // 处理完成的交易查询任务
                    Some(_) = tx_futures.next(), if !tx_futures.is_empty() => {
                        // 任务已经在内部处理完毕，这里不需要额外操作
                    }
                    
                    // 如果没有正在进行的任务且pending_stream已关闭，则退出循环
                    else => {
                        if tx_futures.is_empty() {
                            break;
                        }
                    }
                }
            }
        });

        let receipt_stream = Box::pin(ReceiverStream::new(receipt_rx));
        Ok(receipt_stream)
    }

    pub async fn subscribe_logs() -> Result<Pin<Box<dyn Stream<Item =  Vec<Address>> + Send>>, DEXError> {
        // 创建专用通道 - 用于发送有状态变化的池地址
        let (pool_tx, pool_rx) = mpsc::channel(16);

        // 克隆所需数据
        let provider = &VIRA.get().unwrap().connector.provider;
        let pools = &VIRA.get().unwrap().sm.pools;

        tokio::spawn(async move {
            use futures::stream::FuturesUnordered;
            
            // 获取所有池的地址并创建日志过滤器
            let addrs = pools.data.iter().map(|entry| *entry.key()).collect::<Vec<_>>();
            if addrs.is_empty() {
                println!("No pools to subscribe to");
                return;
            }
            
            let filter = Filter::new()
                .address(addrs)
                .from_block(BlockNumberOrTag::Latest);
            
            // 订阅日志事件
            let mut log_stream = match provider.subscribe_logs(&filter).await {
                Ok(sub) => sub.into_stream(),
                Err(e) => {
                    println!("Log subscription failed: {e}");
                    return;
                }
            };
            
            // 创建一个FuturesUnordered来管理并发的日志处理任务
            let mut log_futures = FuturesUnordered::new();

            loop {
                tokio::select! {
                    // 处理新的日志事件
                    Some(log) = log_stream.next() => {
                        // 创建日志处理任务并添加到FuturesUnordered
                        let pool_tx_clone = pool_tx.clone();
                        
                        log_futures.push(async move {
                            let address = log.address();
                            
                            // 尝试获取并更新对应的池
                            if let Some(mut pool) = pools.data.get_mut(&address) {
                                // 同步池状态
                                match pool.sync(&log) {
                                    Ok(is_changed) => {
                                        if is_changed {
                                            // 如果池状态有变化，发送池地址
                                            let _ = pool_tx_clone.send(vec![address]).await;
                                        }
                                    }
                                    Err(e) => {
                                        println!("Pool sync failed for {}: {:?}", address, e);
                                    }
                                }
                            }
                            true // 返回处理成功的标志
                        });
                    }
                    
                    // 处理完成的日志处理任务
                    Some(_) = log_futures.next(), if !log_futures.is_empty() => {
                        // 任务已经在内部处理完毕，这里不需要额外操作
                    }
                    
                    // 如果没有正在进行的任务且log_stream已关闭，则退出循环
                    else => {
                        if log_futures.is_empty() {
                            println!("{}", "subscribe_block subscribe_block_and_logs closed, stopping...".to_string().red());
                            break;
                        }
                    }
                }
            }
        });

        // 创建专用流
        let pool_stream = Box::pin(ReceiverStream::new(pool_rx));

        Ok(pool_stream)
    }

}

#[repr(align(64))]
pub struct ChainStatus {
    //这两个属性同步更新，不需要对齐
    latest_block: AtomicU64,

    //legacy gas
    gas_price : AtomicU64,
    support_get_gas_price : OnceLock<bool>,

    //eip1559
    base_fee_per_gas : AtomicU64,
    priority_fee : AtomicU64,
    
    is_eip1559 : OnceLock<bool>,
    chain_id : OnceLock<u64>,
}

impl ChainStatus {
    pub fn new() -> Self {
        Self {
            latest_block: AtomicU64::new(0),
            gas_price : AtomicU64::new(10010001), //0.1gwei
            support_get_gas_price : OnceLock::new(),
            base_fee_per_gas: AtomicU64::new(0),
            priority_fee: AtomicU64::new(0),
            chain_id: OnceLock::new(),
            is_eip1559: OnceLock::new(),
        }
    }

    pub fn chain_id(&self) -> u64 {
        *self.chain_id.get().expect("empty chain_id")
    }

    pub fn is_eip1559(&self) -> bool {
        *self.is_eip1559.get().expect("empty is_eip1559")
    }

    pub fn gas_price(&self) -> u64 {
        self.gas_price.load(Ordering::Relaxed)
    }

    pub fn update_gas_price(&self, gas_price: u128) {
        self.gas_price.store(gas_price as u64, Ordering::Relaxed);
    }

    pub fn update_gas_price_from_cfg(&self, gas_price : f32){
        //gwei to u128
        self.gas_price.store((gas_price * 1000000000.0) as u64, Ordering::Relaxed);
    }

    pub fn support_get_gas_price(&self) -> bool {
        *self.support_get_gas_price.get().expect("empty support_get_gas_price")
    }

    pub fn latest_block(&self) -> u64 {
        self.latest_block.load(Ordering::Relaxed)
    }

    pub fn update_latest_block(&self, block: u64) {
        let latest_block = self.latest_block();
        if block > latest_block {
            self.latest_block.store(block, Ordering::Relaxed);
        }
    }

    pub fn base_fee_per_gas(&self) -> u64 {
        self.base_fee_per_gas.load(Ordering::Relaxed)
    }

    pub fn update_base_fee(&self, base_fee: Option<u64>) {
        if let Some(fee) = base_fee {
            self.base_fee_per_gas.store(fee, Ordering::Relaxed);
        }
    }

    pub fn priority_fee(&self) -> u64 {
        self.priority_fee.load(Ordering::Relaxed)
    }

}